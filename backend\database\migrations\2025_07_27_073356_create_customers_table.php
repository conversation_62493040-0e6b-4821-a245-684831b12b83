<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('customer_code')->unique(); // رمز العميل
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone2')->nullable(); // هاتف إضافي
            $table->string('tax_number')->nullable();
            $table->string('commercial_register')->nullable(); // السجل التجاري
            $table->text('address')->nullable();
            $table->text('address_en')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable(); // المنطقة
            $table->string('country')->default('SA');
            $table->string('postal_code')->nullable();
            $table->enum('type', ['individual', 'company'])->default('individual');
            $table->enum('category', ['regular', 'vip', 'wholesale', 'retail'])->default('regular');
            $table->decimal('credit_limit', 12, 2)->default(0);
            $table->decimal('current_balance', 12, 2)->default(0);
            $table->integer('credit_days')->default(0); // أيام الائتمان
            $table->decimal('discount_rate', 5, 2)->default(0); // نسبة خصم العميل
            $table->date('birth_date')->nullable(); // تاريخ الميلاد للأفراد
            $table->date('registration_date')->nullable(); // تاريخ التسجيل للشركات
            $table->string('contact_person')->nullable(); // الشخص المسؤول
            $table->string('website')->nullable();
            $table->json('social_media')->nullable(); // وسائل التواصل الاجتماعي
            $table->boolean('is_active')->default(true);
            $table->boolean('send_notifications')->default(true);
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable(); // حقول مخصصة
            $table->timestamps();

            // الفهارس
            $table->index(['company_id', 'is_active']);
            $table->index('customer_code');
            $table->index(['type', 'category']);
            $table->index('phone');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
