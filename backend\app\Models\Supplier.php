<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    protected $fillable = [
        'company_id',
        'supplier_code',
        'name',
        'name_en',
        'contact_person',
        'contact_person_en',
        'email',
        'phone',
        'phone2',
        'fax',
        'tax_number',
        'commercial_register',
        'address',
        'address_en',
        'city',
        'state',
        'country',
        'postal_code',
        'website',
        'type',
        'category',
        'credit_limit',
        'current_balance',
        'payment_terms',
        'discount_rate',
        'bank_name',
        'bank_account',
        'iban',
        'certifications',
        'products_categories',
        'is_active',
        'is_preferred',
        'notes',
        'custom_fields'
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'certifications' => 'array',
        'products_categories' => 'array',
        'custom_fields' => 'array',
        'is_active' => 'boolean',
        'is_preferred' => 'boolean'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function purchases(): HasMany
    {
        return $this->hasMany(Purchase::class);
    }

    // الدوال المساعدة
    public function getTotalPurchases(): float
    {
        return $this->purchases()->sum('total_amount');
    }

    public function getPendingOrdersCount(): int
    {
        return $this->purchases()->where('status', 'pending')->count();
    }

    public function getLastPurchaseDate()
    {
        return $this->purchases()->latest()->first()?->purchase_date;
    }

    public function getAverageOrderValue(): float
    {
        return $this->purchases()->avg('total_amount') ?? 0;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePreferred($query)
    {
        return $query->where('is_preferred', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('name_en', 'like', "%{$term}%")
              ->orWhere('supplier_code', 'like', "%{$term}%")
              ->orWhere('email', 'like', "%{$term}%")
              ->orWhere('phone', 'like', "%{$term}%");
        });
    }

    // أنواع الموردين
    public static function getTypes(): array
    {
        return [
            'local' => 'محلي',
            'international' => 'دولي'
        ];
    }

    // فئات الموردين
    public static function getCategories(): array
    {
        return [
            'manufacturer' => 'مصنع',
            'distributor' => 'موزع',
            'wholesaler' => 'تاجر جملة',
            'retailer' => 'تاجر تجزئة'
        ];
    }
}
