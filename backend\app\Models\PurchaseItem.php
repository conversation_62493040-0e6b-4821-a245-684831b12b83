<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseItem extends Model
{
    protected $fillable = [
        'purchase_id',
        'product_id',
        'quantity_ordered',
        'quantity_received',
        'unit_cost',
        'total_cost',
        'notes'
    ];

    protected $casts = [
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2'
    ];

    // العلاقات
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // الدوال المساعدة
    public function isFullyReceived(): bool
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    public function isPartiallyReceived(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    public function isNotReceived(): bool
    {
        return $this->quantity_received === 0;
    }

    public function getRemainingQuantity(): int
    {
        return max(0, $this->quantity_ordered - $this->quantity_received);
    }

    public function getReceiveProgress(): float
    {
        if ($this->quantity_ordered === 0) {
            return 0;
        }
        
        return ($this->quantity_received / $this->quantity_ordered) * 100;
    }

    public function receiveQuantity(int $quantity, string $notes = null): bool
    {
        if ($quantity <= 0 || $quantity > $this->getRemainingQuantity()) {
            return false;
        }

        $this->quantity_received += $quantity;
        if ($notes) {
            $this->notes = $notes;
        }
        $this->save();

        // تحديث مخزون المنتج
        $this->product->updateStock($quantity, 'purchase', "استلام من طلب شراء #{$this->purchase->purchase_number}");

        // تحديث حالة طلب الشراء
        $this->purchase->updateStatus();

        return true;
    }

    // Mutators
    public function setTotalCostAttribute($value)
    {
        $this->attributes['total_cost'] = $this->quantity_ordered * $this->unit_cost;
    }

    // Accessors
    public function getTotalCostAttribute()
    {
        return $this->quantity_ordered * $this->unit_cost;
    }
}
