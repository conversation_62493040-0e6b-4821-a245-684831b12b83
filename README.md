# نظام نقاط البيع المتكامل - Wisaq POS System

## وصف المشروع

نظام نقاط بيع (POS) متكامل وحديث مع نظام اشتراكات (SaaS) مخصص للمحلات والمتاجر والشركات، يعمل عبر الويب مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏪 نظام نقاط البيع (POS)
- واجهة سريعة وسهلة الاستخدام للكاشيرين
- دعم قراءة الباركود عبر الأجهزة التقليدية أو كاميرا الويب
- إدارة السلة مع إمكانية تطبيق خصومات وضرائب
- دعم طرق دفع متعددة (نقدي، بطاقة، تحويل بنكي)
- طباعة الفواتير الضريبية باللغة العربية

### 📊 لوحة التحكم والتقارير
- إحصائيات مبيعات يومية وشهرية وسنوية
- تقارير مفصلة للمنتجات والعملاء
- مخططات بيانية تفاعلية
- تنبيهات المخزون المنخفض
- متابعة أداء الفروع والموظفين

### 🏢 إدارة الشركات والفروع
- دعم الشركات متعددة الفروع
- نظام صلاحيات متقدم للمستخدمين
- إدارة الموظفين والكاشيرين
- تخصيص الإعدادات لكل فرع

### 💳 نظام الاشتراكات (SaaS)
- باقات اشتراك مرنة (أساسي، متقدم، احترافي)
- دفع آمن عبر بوابة Tap
- تجديد تلقائي للاشتراكات
- إدارة الفواتير والمدفوعات

### 📦 إدارة المخزون
- تتبع المخزون في الوقت الفعلي
- تنبيهات انخفاض المخزون
- إدارة الموردين والمشتريات
- جرد دوري وتقارير المخزون
- دعم استيراد البيانات من Excel/CSV

### 🔒 الأمان والحماية
- مصادقة آمنة باستخدام JWT
- حماية من CSRF, XSS, SQL Injection
- تشفير البيانات الحساسة
- سجل نشاط المستخدمين
- نسخ احتياطية تلقائية

## البنية التقنية

### Frontend
- **React 18** مع TypeScript
- **Tailwind CSS** + **shadcn/ui** للتصميم
- **React Router** للتنقل
- **React Query** لإدارة البيانات
- **Vite** كأداة البناء
- **Lucide React** للأيقونات

### Backend
- **Laravel 10** مع PHP 8.2+
- **MySQL 8.0** كقاعدة البيانات
- **Laravel Sanctum** للمصادقة
- **Spatie Permissions** لإدارة الصلاحيات
- **Tap Payment API** للدفع الإلكتروني
- **RESTful APIs** للتكامل

## متطلبات التشغيل

### متطلبات الخادم
- **PHP 8.2** أو أحدث
- **Node.js 20.19** أو أحدث
- **MySQL 8.0** أو أحدث
- **Composer** لإدارة حزم PHP
- **NPM** لإدارة حزم JavaScript

### متطلبات إضافية
- **SSL Certificate** للأمان
- **Redis** (اختياري) للتخزين المؤقت
- **Supervisor** لإدارة المهام في الخلفية

## التثبيت والإعداد

### 1. إعداد Backend (Laravel)

```bash
# الانتقال إلى مجلد Backend
cd backend

# تثبيت dependencies
composer install

# نسخ ملف البيئة
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate

# إعداد قاعدة البيانات في .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wisaq_pos
DB_USERNAME=root
DB_PASSWORD=your_password

# إعداد Tap Payment
TAP_SECRET_KEY=your_tap_secret_key
TAP_PUBLIC_KEY=your_tap_public_key

# تشغيل migrations
php artisan migrate

# إضافة البيانات التجريبية
php artisan db:seed

# تشغيل الخادم
php artisan serve
```

### 2. إعداد Frontend (React)

```bash
# تثبيت dependencies
npm install

# تشغيل خادم التطوير
npm run dev

# أو للإنتاج
npm run build
```

### 3. إعداد قاعدة البيانات

قم بإنشاء قاعدة بيانات MySQL جديدة:

```sql
CREATE DATABASE wisaq_pos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. إعداد بوابة الدفع Tap

1. سجل حساب في [Tap Payments](https://www.tap.company/)
2. احصل على API Keys من لوحة التحكم
3. أضف المفاتيح في ملف `.env`:

```env
TAP_SECRET_KEY=sk_test_your_secret_key
TAP_PUBLIC_KEY=pk_test_your_public_key
TAP_WEBHOOK_SECRET=your_webhook_secret
```

## بيانات الدخول التجريبية

بعد تشغيل `php artisan db:seed`:

### مدير النظام
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

### كاشير
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** password

## هيكل قاعدة البيانات

### الجداول الرئيسية

- `companies` - بيانات الشركات
- `branches` - فروع الشركات
- `users` - المستخدمين والموظفين
- `products` - المنتجات
- `categories` - تصنيفات المنتجات
- `sales` - المبيعات والفواتير
- `sale_items` - بنود الفواتير
- `customers` - العملاء
- `suppliers` - الموردين
- `subscriptions` - اشتراكات الشركات
- `payments` - مدفوعات Tap
- `inventory_logs` - سجل حركة المخزون

## API Documentation

### Authentication Endpoints

```
POST /api/v1/login          - تسجيل الدخول
POST /api/v1/logout         - تسجيل الخروج
GET  /api/v1/me             - بيانات المستخدم الحالي
PUT  /api/v1/password       - تغيير كلمة المرور
```

### Dashboard Endpoints

```
GET /api/v1/dashboard/stats        - إحصائيات لوحة التحكم
GET /api/v1/dashboard/recent-sales - المبيعات الأخيرة
GET /api/v1/dashboard/top-products - أفضل المنتجات
GET /api/v1/dashboard/sales-chart  - بيانات مخطط المبيعات
```

### Products Endpoints

```
GET    /api/v1/products              - قائمة المنتجات
POST   /api/v1/products              - إضافة منتج جديد
GET    /api/v1/products/{id}         - تفاصيل منتج
PUT    /api/v1/products/{id}         - تحديث منتج
DELETE /api/v1/products/{id}         - حذف منتج
GET    /api/v1/products/search/{term} - البحث في المنتجات
GET    /api/v1/products/barcode/{code} - البحث بالباركود
```

### Sales Endpoints

```
GET  /api/v1/sales           - قائمة المبيعات
POST /api/v1/sales/pos       - إنشاء بيع جديد من POS
GET  /api/v1/sales/{id}      - تفاصيل بيع
POST /api/v1/sales/{id}/refund - استرداد بيع
```

## دليل الاستخدام

### 1. إعداد الشركة الأولى

1. سجل دخول كمدير نظام
2. انتقل إلى "إدارة الشركات"
3. أضف شركة جديدة مع البيانات الأساسية
4. حدد باقة الاشتراك المناسبة
5. أضف الفرع الرئيسي للشركة

### 2. إضافة المستخدمين

1. انتقل إلى "إدارة المستخدمين"
2. أضف مستخدم جديد مع تحديد الدور:
   - **مدير الشركة:** صلاحيات كاملة
   - **مدير الفرع:** إدارة فرع واحد
   - **كاشير:** نقاط البيع فقط
   - **محاسب:** التقارير والمالية

### 3. إعداد المنتجات

1. انتقل إلى "إدارة المخزون"
2. أضف التصنيفات أولاً
3. أضف المنتجات مع:
   - الاسم بالعربية والإنجليزية
   - رمز المنتج (SKU)
   - الباركود
   - أسعار التكلفة والبيع
   - كمية المخزون

### 4. استخدام نقاط البيع

1. انتقل إلى صفحة "نقاط البيع"
2. ابحث عن المنتجات أو امسح الباركود
3. أضف المنتجات للسلة
4. طبق الخصومات إن وجدت
5. اختر طريقة الدفع
6. اطبع الفاتورة

### 5. مراقبة الأداء

1. راجع لوحة التحكم للإحصائيات اليومية
2. استخدم التقارير المفصلة للتحليل
3. راقب تنبيهات المخزون المنخفض
4. تابع أداء الفروع والموظفين

## المميزات المتقدمة

### 1. نظام الباركود

- دعم جميع أنواع الباركود الشائعة
- قراءة عبر الكاميرا للأجهزة المحمولة
- إنشاء باركود تلقائي للمنتجات الجديدة

### 2. الفواتير الضريبية

- متوافقة مع أنظمة الضرائب السعودية
- حساب ضريبة القيمة المضافة تلقائياً
- طباعة فواتير بتصميم احترافي
- حفظ نسخ إلكترونية

### 3. التقارير المتقدمة

- تقارير المبيعات (يومي، أسبوعي، شهري)
- تحليل أداء المنتجات
- تقارير المخزون والحركة
- تقارير العملاء والموردين
- تصدير التقارير بصيغ مختلفة

### 4. إدارة الفروع

- دعم عدد غير محدود من الفروع
- إعدادات منفصلة لكل فرع
- نقل المخزون بين الفروع
- تقارير موحدة أو منفصلة

### 5. نظام الصلاحيات

- أدوار محددة مسبقاً
- صلاحيات مخصصة
- تحكم دقيق في الوصول
- سجل نشاط المستخدمين

## الأمان والحماية

### 1. حماية البيانات

- تشفير كلمات المرور باستخدام Bcrypt
- حماية من SQL Injection
- حماية من XSS و CSRF
- تشفير البيانات الحساسة

### 2. المصادقة والتخويل

- نظام JWT للمصادقة
- انتهاء صلاحية الجلسات
- تسجيل خروج تلقائي
- مصادقة ثنائية (قريباً)

### 3. النسخ الاحتياطية

- نسخ احتياطية تلقائية يومية
- تشفير ملفات النسخ الاحتياطية
- استعادة سريعة للبيانات
- تخزين آمن في السحابة

## الدعم والصيانة

### 1. المراقبة

- مراقبة أداء النظام
- تنبيهات الأخطاء
- إحصائيات الاستخدام
- سجلات مفصلة

### 2. التحديثات

- تحديثات أمنية دورية
- مميزات جديدة شهرياً
- إصلاح الأخطاء السريع
- دعم فني متواصل

### 3. التدريب

- دليل المستخدم المفصل
- فيديوهات تعليمية
- ورش عمل للموظفين
- دعم فني مباشر

## الترخيص والاستخدام

هذا النظام مطور خصيصاً كنظام SaaS متكامل. جميع الحقوق محفوظة.

### شروط الاستخدام

- استخدام تجاري مسموح للمشتركين
- منع إعادة التوزيع أو البيع
- الالتزام بشروط الخدمة
- احترام حقوق الملكية الفكرية

## التواصل والدعم

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966 50 123 4567
- **الموقع الإلكتروني:** https://wisaq.com
- **الدعم الفني:** متاح 24/7

---

**تم تطوير هذا النظام بأحدث التقنيات لضمان الأداء العالي والأمان المتقدم**

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/6828d29e-64a7-4e9a-9062-b23f150b1727) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
