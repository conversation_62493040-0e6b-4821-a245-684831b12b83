<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CategoryController extends Controller
{
    /**
     * عرض قائمة الفئات
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::with(['company', 'products'])
                         ->where('company_id', auth()->user()->company_id);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // التصفح أو الكل
        if ($request->get('all', false)) {
            $categories = $query->get();
        } else {
            $perPage = $request->get('per_page', 15);
            $categories = $query->paginate($perPage);
        }

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * إنشاء فئة جديدة
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:50',
            'image' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $category = Category::create(array_merge($validator->validated(), [
            'company_id' => auth()->user()->company_id
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الفئة بنجاح',
            'data' => $category->load('company')
        ], 201);
    }

    /**
     * عرض تفاصيل فئة
     */
    public function show($id): JsonResponse
    {
        $category = Category::with(['company', 'products' => function($query) {
                                $query->where('is_active', true);
                            }])
                           ->where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // إحصائيات الفئة
        $stats = [
            'total_products' => $category->products()->where('is_active', true)->count(),
            'total_stock_value' => $category->products()
                                          ->where('is_active', true)
                                          ->sum(\DB::raw('stock_quantity * cost_price')),
            'low_stock_products' => $category->products()
                                           ->where('is_active', true)
                                           ->whereRaw('stock_quantity <= min_stock_level')
                                           ->count()
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'category' => $category,
                'stats' => $stats
            ]
        ]);
    }

    /**
     * تحديث بيانات فئة
     */
    public function update(Request $request, $id): JsonResponse
    {
        $category = Category::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:50',
            'image' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $category->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث بيانات الفئة بنجاح',
            'data' => $category->load('company')
        ]);
    }

    /**
     * حذف فئة
     */
    public function destroy($id): JsonResponse
    {
        $category = Category::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // التحقق من وجود منتجات في الفئة
        if ($category->products()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف الفئة لوجود منتجات مرتبطة بها'
            ], 400);
        }

        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الفئة بنجاح'
        ]);
    }

    /**
     * البحث في الفئات
     */
    public function search(Request $request): JsonResponse
    {
        $term = $request->get('term', '');
        
        $categories = Category::where('company_id', auth()->user()->company_id)
                             ->where('is_active', true)
                             ->where(function($query) use ($term) {
                                 $query->where('name', 'like', "%{$term}%")
                                       ->orWhere('name_en', 'like', "%{$term}%");
                             })
                             ->orderBy('sort_order')
                             ->limit(10)
                             ->get(['id', 'name', 'name_en', 'color']);

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * إعادة ترتيب الفئات
     */
    public function reorder(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'categories' => 'required|array',
            'categories.*.id' => 'required|integer|exists:categories,id',
            'categories.*.sort_order' => 'required|integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        foreach ($request->categories as $categoryData) {
            Category::where('id', $categoryData['id'])
                   ->where('company_id', auth()->user()->company_id)
                   ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'تم إعادة ترتيب الفئات بنجاح'
        ]);
    }

    /**
     * الحصول على منتجات الفئة
     */
    public function products($id, Request $request): JsonResponse
    {
        $category = Category::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        $query = $category->products()->where('is_active', true);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // فلترة المخزون المنخفض
        if ($request->get('low_stock', false)) {
            $query->whereRaw('stock_quantity <= min_stock_level');
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $perPage = $request->get('per_page', 15);
        $products = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'category' => $category,
                'products' => $products
            ]
        ]);
    }

    /**
     * نقل منتجات إلى فئة أخرى
     */
    public function moveProducts(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'target_category_id' => 'required|integer|exists:categories,id',
            'product_ids' => 'required|array',
            'product_ids.*' => 'integer|exists:products,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $category = Category::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        $targetCategory = Category::where('company_id', auth()->user()->company_id)
                                 ->findOrFail($request->target_category_id);

        // نقل المنتجات
        $category->products()
                 ->whereIn('id', $request->product_ids)
                 ->update(['category_id' => $targetCategory->id]);

        return response()->json([
            'success' => true,
            'message' => 'تم نقل المنتجات بنجاح'
        ]);
    }
}
