<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Product extends Model
{
    protected $fillable = [
        'company_id',
        'category_id',
        'name',
        'name_en',
        'description',
        'description_en',
        'sku',
        'barcode',
        'qr_code',
        'cost_price',
        'selling_price',
        'wholesale_price',
        'discount_price',
        'stock_quantity',
        'min_stock_level',
        'max_stock_level',
        'reorder_level',
        'unit',
        'unit_en',
        'weight',
        'dimensions',
        'color',
        'size',
        'brand',
        'manufacturer',
        'origin_country',
        'image',
        'images',
        'track_stock',
        'is_active',
        'is_featured',
        'allow_backorder',
        'expires_at',
        'attributes',
        'variants',
        'tax_rate',
        'sort_order'
    ];

    protected $casts = [
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'wholesale_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'weight' => 'decimal:3',
        'tax_rate' => 'decimal:2',
        'images' => 'array',
        'attributes' => 'array',
        'variants' => 'array',
        'track_stock' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'allow_backorder' => 'boolean',
        'expires_at' => 'date'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    public function saleItems(): HasMany
    {
        return $this->hasMany(SaleItem::class);
    }

    public function inventoryLogs(): HasMany
    {
        return $this->hasMany(InventoryLog::class);
    }

    public function taxes(): BelongsToMany
    {
        return $this->belongsToMany(Tax::class, 'product_taxes');
    }

    // الدوال المساعدة
    public function isLowStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= $this->min_stock_level;
    }

    public function isOutOfStock(): bool
    {
        return $this->track_stock && $this->stock_quantity <= 0;
    }

    public function canSell($quantity = 1): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if (!$this->track_stock) {
            return true;
        }

        return $this->stock_quantity >= $quantity || $this->allow_backorder;
    }

    public function getEffectivePrice(): float
    {
        return $this->discount_price ?? $this->selling_price;
    }

    public function getProfitMargin(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }

        return (($this->getEffectivePrice() - $this->cost_price) / $this->cost_price) * 100;
    }

    public function getTotalSold(): int
    {
        return $this->saleItems()->sum('quantity');
    }

    public function getTotalRevenue(): float
    {
        return $this->saleItems()->sum('total_price');
    }

    public function updateStock($quantity, $type = 'sale', $notes = null): void
    {
        if (!$this->track_stock) {
            return;
        }

        $oldQuantity = $this->stock_quantity;

        switch ($type) {
            case 'sale':
                $this->stock_quantity -= $quantity;
                break;
            case 'purchase':
            case 'adjustment_in':
                $this->stock_quantity += $quantity;
                break;
            case 'adjustment_out':
                $this->stock_quantity -= $quantity;
                break;
        }

        $this->save();

        // تسجيل حركة المخزون
        InventoryLog::create([
            'product_id' => $this->id,
            'type' => $type,
            'quantity_before' => $oldQuantity,
            'quantity_changed' => $quantity,
            'quantity_after' => $this->stock_quantity,
            'notes' => $notes,
            'user_id' => auth()->id(),
            'branch_id' => auth()->user()?->branch_id
        ]);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeLowStock($query)
    {
        return $query->where('track_stock', true)
                    ->whereRaw('stock_quantity <= min_stock_level');
    }

    public function scopeOutOfStock($query)
    {
        return $query->where('track_stock', true)
                    ->where('stock_quantity', '<=', 0);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('name_en', 'like', "%{$term}%")
              ->orWhere('sku', 'like', "%{$term}%")
              ->orWhere('barcode', 'like', "%{$term}%");
        });
    }
}
