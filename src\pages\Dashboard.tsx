import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  Calendar,
  BarChart3,
  Pie<PERSON><PERSON>,
  Clock,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/contexts/LanguageContext';
import { useAuth } from '@/contexts/AuthContext';
import { dashboardAPI, DashboardStats, Sale } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

const Dashboard: React.FC = () => {
  const { t } = useLanguage();
  const { user, company } = useAuth();
  const { toast } = useToast();

  // حالات البيانات
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentSales, setRecentSales] = useState<Sale[]>([]);
  const [topProducts, setTopProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // جلب بيانات لوحة التحكم
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);

        // جلب الإحصائيات
        const statsResponse = await dashboardAPI.getStats();
        if (statsResponse.success) {
          setStats(statsResponse.data);
        }

        // جلب المبيعات الأخيرة
        const salesResponse = await dashboardAPI.getRecentSales();
        if (salesResponse.success) {
          setRecentSales(salesResponse.data || []);
        }

        // جلب أفضل المنتجات
        const productsResponse = await dashboardAPI.getTopProducts();
        if (productsResponse.success) {
          setTopProducts(productsResponse.data || []);
        }

      } catch (error: any) {
        console.error('خطأ في جلب بيانات لوحة التحكم:', error);
        toast({
          title: "خطأ",
          description: "فشل في جلب بيانات لوحة التحكم",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [toast]);

  // إعداد بيانات الإحصائيات للعرض
  const statsCards = stats ? [
    {
      title: t('dashboard.sales.today'),
      value: stats.today_sales.amount,
      currency: 'ر.س',
      change: stats.today_sales.change,
      changeType: stats.today_sales.change.startsWith('+') ? 'increase' as const : 'decrease' as const,
      icon: DollarSign,
      description: `${stats.today_sales.count} طلب اليوم`
    },
    {
      title: 'مبيعات الشهر',
      value: stats.month_sales.amount,
      currency: 'ر.س',
      change: stats.month_sales.change,
      changeType: stats.month_sales.change.startsWith('+') ? 'increase' as const : 'decrease' as const,
      icon: BarChart3,
      description: 'إجمالي الشهر'
    },
    {
      title: 'المنتجات المتاحة',
      value: stats.products_count.toString(),
      change: '0%',
      changeType: 'increase' as const,
      icon: Package,
      description: 'في المخزون'
    },
    {
      title: 'العملاء النشطين',
      value: stats.customers_count.toString(),
      change: '0%',
      changeType: 'increase' as const,
      icon: Users,
      description: 'عميل مسجل'
    }
  ] : [];



  return (
    <div className="space-y-6">
      {/* ترحيب شخصي */}
      <div className="bg-gradient-primary p-6 rounded-xl text-primary-foreground">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold font-arabic">
              {t('dashboard.welcome')} {user?.name}
            </h1>
            <p className="text-primary-foreground/80 font-arabic mt-1">
              إليك ملخص أداء متجرك اليوم
            </p>
          </div>
          <div className="text-right">
            <p className="text-primary-foreground/80 text-sm">
              {new Date().toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <Clock className="w-4 h-4" />
              <span className="text-sm">
                {new Date().toLocaleTimeString('ar-SA', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="card-interactive">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded"></div>
                <div className="h-4 w-4 bg-muted animate-pulse rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-24 bg-muted animate-pulse rounded mb-2"></div>
                <div className="h-3 w-32 bg-muted animate-pulse rounded"></div>
              </CardContent>
            </Card>
          ))
        ) : (
          statsCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="card-interactive">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium font-arabic">
                    {stat.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold font-english">
                    {stat.value}
                    {stat.currency && (
                      <span className="text-sm text-muted-foreground font-arabic mr-1">
                        {stat.currency}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                    {stat.changeType === 'increase' ? (
                      <TrendingUp className="w-3 h-3 text-success" />
                    ) : (
                      <TrendingDown className="w-3 h-3 text-destructive" />
                    )}
                    <span className={
                      stat.changeType === 'increase' ? 'text-success' : 'text-destructive'
                    }>
                      {stat.change}
                    </span>
                    <span className="font-arabic">{stat.description}</span>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      {/* الصف الثاني من البطاقات */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* المبيعات الأخيرة */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="font-arabic">آخر المبيعات</CardTitle>
            <CardDescription className="font-arabic">
              أحدث المعاملات المالية اليوم
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex-1">
                      <div className="h-4 w-32 bg-muted animate-pulse rounded mb-2"></div>
                      <div className="h-3 w-24 bg-muted animate-pulse rounded"></div>
                    </div>
                    <div className="text-right">
                      <div className="h-4 w-20 bg-muted animate-pulse rounded mb-2"></div>
                      <div className="h-3 w-16 bg-muted animate-pulse rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {recentSales.length > 0 ? recentSales.map((sale) => (
                  <div key={sale.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex-1">
                      <p className="font-medium font-arabic">{sale.customer_name || 'عميل نقدي'}</p>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="font-arabic">فاتورة #{sale.invoice_number}</span>
                        <span>{sale.created_at}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold font-english">
                        {parseFloat(sale.total_amount.toString()).toLocaleString()} ر.س
                      </p>
                      <Badge variant="secondary" className="text-xs font-arabic">
                        {sale.status === 'completed' ? 'مكتملة' : sale.status}
                      </Badge>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <ShoppingCart className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="font-arabic">لا توجد مبيعات اليوم</p>
                  </div>
                )}
              </div>
            )}
            <Button variant="outline" className="w-full mt-4 font-arabic">
              عرض جميع المبيعات
            </Button>
          </CardContent>
        </Card>

        {/* المنتجات الأكثر مبيعاً */}
        <Card className="card-elevated">
          <CardHeader>
            <CardTitle className="font-arabic">المنتجات الأكثر مبيعاً</CardTitle>
            <CardDescription className="font-arabic">
              أفضل المنتجات أداءً هذا الأسبوع
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-muted animate-pulse rounded-lg"></div>
                      <div>
                        <div className="h-4 w-24 bg-muted animate-pulse rounded mb-2"></div>
                        <div className="h-3 w-16 bg-muted animate-pulse rounded"></div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="h-4 w-20 bg-muted animate-pulse rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {topProducts.length > 0 ? topProducts.map((product, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                        <span className="text-primary font-bold text-sm">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium font-arabic text-sm">
                          {product.name}
                        </p>
                        <p className="text-xs text-muted-foreground font-arabic">
                          {product.quantity} مبيعة
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-sm font-english">
                        {parseFloat(product.revenue).toLocaleString()} ر.س
                      </p>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="font-arabic">لا توجد بيانات متاحة</p>
                  </div>
                )}
              </div>
            )}
            <Button variant="outline" className="w-full mt-4 font-arabic">
              عرض تقرير المنتجات
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* إجراءات سريعة */}
      <Card className="card-elevated">
        <CardHeader>
          <CardTitle className="font-arabic">إجراءات سريعة</CardTitle>
          <CardDescription className="font-arabic">
            الأدوات الأكثر استخداماً في نظامك
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col gap-2 btn-gradient-primary">
              <ShoppingCart className="w-6 h-6" />
              <span className="font-arabic">نقطة البيع</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Package className="w-6 h-6" />
              <span className="font-arabic">إدارة المخزون</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <BarChart3 className="w-6 h-6" />
              <span className="font-arabic">التقارير</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Users className="w-6 h-6" />
              <span className="font-arabic">العملاء</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;