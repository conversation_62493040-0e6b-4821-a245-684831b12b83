<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('suppliers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('supplier_code')->unique(); // رمز المورد
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->string('contact_person')->nullable();
            $table->string('contact_person_en')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone2')->nullable(); // هاتف إضافي
            $table->string('fax')->nullable();
            $table->string('tax_number')->nullable();
            $table->string('commercial_register')->nullable();
            $table->text('address')->nullable();
            $table->text('address_en')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->default('SA');
            $table->string('postal_code')->nullable();
            $table->string('website')->nullable();
            $table->enum('type', ['local', 'international'])->default('local');
            $table->enum('category', ['manufacturer', 'distributor', 'wholesaler', 'retailer'])->default('distributor');
            $table->decimal('credit_limit', 12, 2)->default(0);
            $table->decimal('current_balance', 12, 2)->default(0);
            $table->integer('payment_terms')->default(30); // شروط الدفع بالأيام
            $table->decimal('discount_rate', 5, 2)->default(0); // نسبة خصم المورد
            $table->string('bank_name')->nullable();
            $table->string('bank_account')->nullable();
            $table->string('iban')->nullable();
            $table->json('certifications')->nullable(); // الشهادات والتراخيص
            $table->json('products_categories')->nullable(); // فئات المنتجات التي يوردها
            $table->boolean('is_active')->default(true);
            $table->boolean('is_preferred')->default(false); // مورد مفضل
            $table->text('notes')->nullable();
            $table->json('custom_fields')->nullable();
            $table->timestamps();

            // الفهارس
            $table->index(['company_id', 'is_active']);
            $table->index('supplier_code');
            $table->index(['type', 'category']);
            $table->index('phone');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('suppliers');
    }
};
