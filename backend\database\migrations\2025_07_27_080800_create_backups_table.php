<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->nullable(); // null للنسخ الاحتياطية العامة
            $table->string('name');
            $table->enum('type', ['full', 'incremental', 'company_data']); // نوع النسخة الاحتياطية
            $table->enum('status', ['pending', 'running', 'completed', 'failed']);
            $table->string('file_path')->nullable();
            $table->string('file_name')->nullable();
            $table->bigInteger('file_size')->nullable(); // بالبايت
            $table->string('compression_type')->default('zip'); // zip, tar, gzip
            $table->boolean('is_encrypted')->default(true);
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // معلومات إضافية
            $table->boolean('is_automatic')->default(false); // تلقائية أم يدوية
            $table->datetime('expires_at')->nullable(); // تاريخ انتهاء الصلاحية
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            
            $table->index(['company_id', 'status']);
            $table->index(['type', 'is_automatic']);
            $table->index('created_at');
        });

        // جدول استعادة النسخ الاحتياطية
        Schema::create('backup_restores', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('backup_id');
            $table->unsignedBigInteger('user_id'); // المستخدم الذي طلب الاستعادة
            $table->enum('status', ['pending', 'running', 'completed', 'failed']);
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->json('restore_options')->nullable(); // خيارات الاستعادة
            $table->timestamps();

            $table->foreign('backup_id')->references('id')->on('backups')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->index(['backup_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backup_restores');
        Schema::dropIfExists('backups');
    }
};
