<?php

namespace App\Services;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class SecurityService
{
    /**
     * تشفير البيانات الحساسة
     */
    public static function encryptSensitiveData(string $data): string
    {
        try {
            return Crypt::encryptString($data);
        } catch (\Exception $e) {
            Log::error('Encryption failed: ' . $e->getMessage());
            throw new \Exception('فشل في تشفير البيانات');
        }
    }

    /**
     * فك تشفير البيانات الحساسة
     */
    public static function decryptSensitiveData(string $encryptedData): string
    {
        try {
            return Crypt::decryptString($encryptedData);
        } catch (\Exception $e) {
            Log::error('Decryption failed: ' . $e->getMessage());
            throw new \Exception('فشل في فك تشفير البيانات');
        }
    }

    /**
     * إنشاء hash آمن لكلمة المرور
     */
    public static function hashPassword(string $password): string
    {
        return Hash::make($password, [
            'rounds' => 12, // زيادة عدد الجولات للأمان
        ]);
    }

    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword(string $password, string $hash): bool
    {
        return Hash::check($password, $hash);
    }

    /**
     * إنشاء token آمن
     */
    public static function generateSecureToken(int $length = 32): string
    {
        return Str::random($length);
    }

    /**
     * إنشاء API key آمن
     */
    public static function generateApiKey(): string
    {
        return 'wq_' . Str::random(40);
    }

    /**
     * تنظيف البيانات من XSS
     */
    public static function sanitizeInput(string $input): string
    {
        // إزالة العلامات الخطيرة
        $input = strip_tags($input, '<p><br><strong><em><u><ol><ul><li>');
        
        // تحويل الأحرف الخاصة
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        // إزالة JavaScript
        $input = preg_replace('/javascript:/i', '', $input);
        $input = preg_replace('/on\w+\s*=/i', '', $input);
        
        return trim($input);
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public static function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }
        
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }
        
        // التحقق من كلمات المرور الشائعة
        $commonPasswords = [
            '12345678', 'password', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', '123123'
        ];
        
        if (in_array(strtolower($password), $commonPasswords)) {
            $errors[] = 'كلمة المرور ضعيفة جداً، يرجى اختيار كلمة مرور أقوى';
        }
        
        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'strength' => $this->calculatePasswordStrength($password)
        ];
    }

    /**
     * حساب قوة كلمة المرور
     */
    private static function calculatePasswordStrength(string $password): string
    {
        $score = 0;
        
        // الطول
        if (strlen($password) >= 8) $score += 1;
        if (strlen($password) >= 12) $score += 1;
        
        // التنوع
        if (preg_match('/[a-z]/', $password)) $score += 1;
        if (preg_match('/[A-Z]/', $password)) $score += 1;
        if (preg_match('/[0-9]/', $password)) $score += 1;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 1;
        
        // التعقيد
        if (preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) $score += 1;
        
        return match(true) {
            $score <= 2 => 'ضعيفة',
            $score <= 4 => 'متوسطة',
            $score <= 6 => 'قوية',
            default => 'قوية جداً'
        };
    }

    /**
     * التحقق من صحة عنوان IP
     */
    public static function validateIpAddress(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * التحقق من الـ User Agent المشبوه
     */
    public static function isSuspiciousUserAgent(string $userAgent): bool
    {
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/java/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * إنشاء CSRF token
     */
    public static function generateCsrfToken(): string
    {
        return hash('sha256', Str::random(40) . time());
    }

    /**
     * التحقق من CSRF token
     */
    public static function verifyCsrfToken(string $token, string $sessionToken): bool
    {
        return hash_equals($sessionToken, $token);
    }

    /**
     * تسجيل محاولة أمنية مشبوهة
     */
    public static function logSecurityEvent(string $event, array $data = []): void
    {
        Log::channel('security')->warning('Security Event: ' . $event, array_merge($data, [
            'timestamp' => now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => auth()->id(),
        ]));
    }

    /**
     * تنظيف اسم الملف من الأحرف الخطيرة
     */
    public static function sanitizeFileName(string $filename): string
    {
        // إزالة الأحرف الخطيرة
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // منع الملفات المخفية
        $filename = ltrim($filename, '.');
        
        // تحديد الطول الأقصى
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }
        
        return $filename ?: 'file';
    }

    /**
     * التحقق من امتداد الملف المسموح
     */
    public static function isAllowedFileExtension(string $filename, array $allowedExtensions): bool
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, array_map('strtolower', $allowedExtensions));
    }

    /**
     * فحص الملف من الفيروسات (محاكاة)
     */
    public static function scanFileForViruses(string $filePath): bool
    {
        // في التطبيق الحقيقي، يمكن دمج خدمة فحص الفيروسات
        // مثل ClamAV أو خدمة سحابية
        
        // فحص بسيط للملفات المشبوهة
        $suspiciousExtensions = ['exe', 'bat', 'cmd', 'scr', 'pif', 'com'];
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        if (in_array($extension, $suspiciousExtensions)) {
            return false;
        }
        
        // فحص محتوى الملف للكلمات المشبوهة
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath, false, null, 0, 1024); // أول 1KB
            $suspiciousPatterns = ['<script', 'javascript:', 'eval(', 'exec('];
            
            foreach ($suspiciousPatterns as $pattern) {
                if (stripos($content, $pattern) !== false) {
                    return false;
                }
            }
        }
        
        return true;
    }
}
