<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('user_id'); // منشئ التقرير
            $table->string('name');
            $table->string('type'); // sales, inventory, financial, custom
            $table->json('filters'); // فلاتر التقرير
            $table->json('columns'); // الأعمدة المطلوبة
            $table->enum('format', ['table', 'chart', 'both'])->default('table');
            $table->string('chart_type')->nullable(); // line, bar, pie, etc.
            $table->boolean('is_scheduled')->default(false);
            $table->string('schedule_frequency')->nullable(); // daily, weekly, monthly
            $table->json('schedule_config')->nullable();
            $table->boolean('is_public')->default(false); // يمكن مشاركته
            $table->text('description')->nullable();
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->index(['company_id', 'type']);
            $table->index(['user_id', 'is_public']);
        });

        // جدول تشغيل التقارير المجدولة
        Schema::create('report_executions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('report_id');
            $table->enum('status', ['pending', 'running', 'completed', 'failed']);
            $table->datetime('started_at')->nullable();
            $table->datetime('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->string('file_path')->nullable(); // مسار الملف المُصدر
            $table->integer('records_count')->nullable();
            $table->timestamps();

            $table->foreign('report_id')->references('id')->on('reports')->onDelete('cascade');
            
            $table->index(['report_id', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('report_executions');
        Schema::dropIfExists('reports');
    }
};
