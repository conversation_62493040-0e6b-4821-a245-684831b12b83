<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('branch_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('invoice_number')->unique();
            $table->date('sale_date');
            $table->time('sale_time');
            $table->decimal('subtotal', 12, 2);
            $table->decimal('discount_amount', 12, 2)->default(0);
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->decimal('total_amount', 12, 2);
            $table->decimal('paid_amount', 12, 2)->default(0);
            $table->decimal('change_amount', 12, 2)->default(0);
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'credit', 'multiple'])->default('cash');
            $table->json('payment_details')->nullable(); // تفاصيل الدفع المتعدد
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded', 'partial_refund'])->default('completed');
            $table->string('pos_terminal')->nullable(); // رقم الجهاز
            $table->string('receipt_number')->nullable(); // رقم الإيصال
            $table->boolean('is_printed')->default(false);
            $table->boolean('is_tax_invoice')->default(true); // فاتورة ضريبية
            $table->text('notes')->nullable();
            $table->json('tax_details')->nullable();
            $table->json('discount_details')->nullable(); // تفاصيل الخصومات المطبقة
            $table->string('refund_reason')->nullable();
            $table->decimal('refund_amount', 12, 2)->nullable();
            $table->datetime('refund_date')->nullable();
            $table->timestamps();

            // الفهارس
            $table->index(['company_id', 'sale_date']);
            $table->index(['branch_id', 'status']);
            $table->index(['user_id', 'sale_date']);
            $table->index('invoice_number');
            $table->index(['status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales');
    }
};
