<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Tax extends Model
{
    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'rate',
        'type',
        'description',
        'is_default',
        'is_active'
    ];

    protected $casts = [
        'rate' => 'decimal:2',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_taxes');
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'category_taxes');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    // الدوال المساعدة
    public function calculateTax($amount): float
    {
        return match($this->type) {
            'inclusive' => $amount - ($amount / (1 + ($this->rate / 100))),
            'exclusive' => $amount * ($this->rate / 100),
            default => 0
        };
    }

    public function calculateAmountWithTax($amount): float
    {
        return match($this->type) {
            'inclusive' => $amount,
            'exclusive' => $amount + $this->calculateTax($amount),
            default => $amount
        };
    }

    public function calculateAmountWithoutTax($amount): float
    {
        return match($this->type) {
            'inclusive' => $amount / (1 + ($this->rate / 100)),
            'exclusive' => $amount,
            default => $amount
        };
    }

    // أنواع الضرائب
    public static function getTypes(): array
    {
        return [
            'inclusive' => 'شاملة للضريبة',
            'exclusive' => 'إضافية للضريبة'
        ];
    }

    // الحصول على الضريبة الافتراضية للشركة
    public static function getDefaultTax($companyId)
    {
        return static::where('company_id', $companyId)
                    ->where('is_default', true)
                    ->where('is_active', true)
                    ->first();
    }

    // تعيين كضريبة افتراضية
    public function setAsDefault(): void
    {
        // إلغاء الافتراضية من الضرائب الأخرى
        static::where('company_id', $this->company_id)
              ->where('id', '!=', $this->id)
              ->update(['is_default' => false]);

        // تعيين هذه الضريبة كافتراضية
        $this->update(['is_default' => true]);
    }
}
