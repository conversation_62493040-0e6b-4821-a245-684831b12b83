<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->text('description_en')->nullable();
            $table->string('sku')->unique();
            $table->string('barcode')->nullable();
            $table->string('qr_code')->nullable();
            $table->decimal('cost_price', 10, 2)->default(0);
            $table->decimal('selling_price', 10, 2);
            $table->decimal('wholesale_price', 10, 2)->nullable();
            $table->decimal('discount_price', 10, 2)->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->integer('min_stock_level')->default(0);
            $table->integer('max_stock_level')->nullable();
            $table->integer('reorder_level')->nullable(); // مستوى إعادة الطلب
            $table->string('unit')->default('piece');
            $table->string('unit_en')->default('piece');
            $table->decimal('weight', 8, 3)->nullable();
            $table->string('dimensions')->nullable(); // الأبعاد
            $table->string('color')->nullable();
            $table->string('size')->nullable();
            $table->string('brand')->nullable(); // العلامة التجارية
            $table->string('manufacturer')->nullable(); // الشركة المصنعة
            $table->string('origin_country')->nullable(); // بلد المنشأ
            $table->string('image')->nullable();
            $table->json('images')->nullable();
            $table->boolean('track_stock')->default(true);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false); // منتج مميز
            $table->boolean('allow_backorder')->default(false); // السماح بالطلب عند نفاد المخزون
            $table->date('expires_at')->nullable();
            $table->json('attributes')->nullable(); // خصائص إضافية
            $table->json('variants')->nullable(); // متغيرات المنتج
            $table->decimal('tax_rate', 5, 2)->nullable(); // معدل الضريبة الخاص
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->timestamps();

            // الفهارس
            $table->index(['company_id', 'is_active']);
            $table->index(['category_id', 'is_active']);
            $table->index('sku');
            $table->index('barcode');
            $table->index(['stock_quantity', 'min_stock_level']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
