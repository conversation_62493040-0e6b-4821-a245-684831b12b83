<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Discount extends Model
{
    protected $fillable = [
        'company_id',
        'name',
        'name_en',
        'description',
        'code',
        'type',
        'value',
        'min_amount',
        'max_discount',
        'applies_to',
        'applies_to_ids',
        'usage_limit',
        'used_count',
        'starts_at',
        'expires_at',
        'is_active'
    ];

    protected $casts = [
        'applies_to_ids' => 'array',
        'value' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_discount' => 'decimal:2',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function usages(): HasMany
    {
        return $this->hasMany(DiscountUsage::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('starts_at', '<=', now())
                    ->where('expires_at', '>=', now());
    }

    public function scopeByCode($query, $code)
    {
        return $query->where('code', $code);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    // الدوال المساعدة
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->starts_at && $this->starts_at > now()) {
            return false;
        }

        if ($this->expires_at && $this->expires_at < now()) {
            return false;
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    public function canApplyTo($item): bool
    {
        if ($this->applies_to === 'all') {
            return true;
        }

        $appliesTo = $this->applies_to_ids ?? [];

        switch ($this->applies_to) {
            case 'products':
                return in_array($item->id, $appliesTo);
            case 'categories':
                return in_array($item->category_id, $appliesTo);
            case 'customers':
                return in_array($item->id, $appliesTo);
            default:
                return false;
        }
    }

    public function calculateDiscount($amount): float
    {
        if (!$this->isValid()) {
            return 0;
        }

        if ($this->min_amount && $amount < $this->min_amount) {
            return 0;
        }

        $discount = match($this->type) {
            'percentage' => $amount * ($this->value / 100),
            'fixed_amount' => $this->value,
            default => 0
        };

        if ($this->max_discount && $discount > $this->max_discount) {
            $discount = $this->max_discount;
        }

        return round($discount, 2);
    }

    public function incrementUsage(): void
    {
        $this->increment('used_count');
    }

    public function decrementUsage(): void
    {
        if ($this->used_count > 0) {
            $this->decrement('used_count');
        }
    }

    // أنواع الخصومات
    public static function getTypes(): array
    {
        return [
            'percentage' => 'نسبة مئوية',
            'fixed_amount' => 'مبلغ ثابت'
        ];
    }

    // ما ينطبق عليه الخصم
    public static function getAppliesTo(): array
    {
        return [
            'all' => 'جميع المنتجات',
            'categories' => 'فئات محددة',
            'products' => 'منتجات محددة',
            'customers' => 'عملاء محددين'
        ];
    }
}
