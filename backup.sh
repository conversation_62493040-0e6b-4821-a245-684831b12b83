#!/bin/bash

# Wisaq POS System Backup Script
# This script creates automated backups of the database and files

set -e

# Configuration
BACKUP_DIR="/var/backups/wisaq"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="wisaq_pos"
DB_USER="wisaq_user"
DB_PASS="wisaq_password"
RETENTION_DAYS=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
mkdir -p $BACKUP_DIR

print_status "Starting backup process..."

# Database backup
print_status "Creating database backup..."
docker-compose exec -T mysql mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database_$DATE.sql

if [ $? -eq 0 ]; then
    print_status "Database backup completed successfully"
    gzip $BACKUP_DIR/database_$DATE.sql
else
    print_error "Database backup failed"
    exit 1
fi

# Files backup
print_status "Creating files backup..."
tar -czf $BACKUP_DIR/files_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='.git' \
    --exclude='storage/logs' \
    --exclude='storage/framework/cache' \
    --exclude='storage/framework/sessions' \
    --exclude='storage/framework/views' \
    ./

if [ $? -eq 0 ]; then
    print_status "Files backup completed successfully"
else
    print_error "Files backup failed"
    exit 1
fi

# Storage backup
print_status "Creating storage backup..."
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz backend/storage/app/public/

if [ $? -eq 0 ]; then
    print_status "Storage backup completed successfully"
else
    print_error "Storage backup failed"
    exit 1
fi

# Clean old backups
print_status "Cleaning old backups (older than $RETENTION_DAYS days)..."
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +$RETENTION_DAYS -delete

# Create backup summary
BACKUP_SIZE=$(du -sh $BACKUP_DIR | cut -f1)
echo "Backup completed at: $(date)" > $BACKUP_DIR/backup_$DATE.log
echo "Database backup: database_$DATE.sql.gz" >> $BACKUP_DIR/backup_$DATE.log
echo "Files backup: files_$DATE.tar.gz" >> $BACKUP_DIR/backup_$DATE.log
echo "Storage backup: storage_$DATE.tar.gz" >> $BACKUP_DIR/backup_$DATE.log
echo "Total backup size: $BACKUP_SIZE" >> $BACKUP_DIR/backup_$DATE.log

print_status "✅ Backup process completed successfully!"
print_status "📁 Backup location: $BACKUP_DIR"
print_status "💾 Total size: $BACKUP_SIZE"

# Optional: Upload to cloud storage (uncomment and configure as needed)
# print_status "Uploading to cloud storage..."
# aws s3 cp $BACKUP_DIR/database_$DATE.sql.gz s3://your-backup-bucket/wisaq/
# aws s3 cp $BACKUP_DIR/files_$DATE.tar.gz s3://your-backup-bucket/wisaq/
# aws s3 cp $BACKUP_DIR/storage_$DATE.tar.gz s3://your-backup-bucket/wisaq/
