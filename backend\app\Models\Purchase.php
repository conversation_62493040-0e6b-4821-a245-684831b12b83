<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Purchase extends Model
{
    protected $fillable = [
        'company_id',
        'branch_id',
        'supplier_id',
        'user_id',
        'purchase_number',
        'purchase_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'status',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'shipping_cost',
        'total_amount',
        'notes',
        'attachments'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'attachments' => 'array'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(PurchaseItem::class);
    }

    // الدوال المساعدة
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isOrdered(): bool
    {
        return $this->status === 'ordered';
    }

    public function isReceived(): bool
    {
        return $this->status === 'received';
    }

    public function isPartiallyReceived(): bool
    {
        return $this->status === 'partial_received';
    }

    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    public function canBeReceived(): bool
    {
        return in_array($this->status, ['ordered', 'partial_received']);
    }

    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'ordered']);
    }

    public function getTotalItemsCount(): int
    {
        return $this->items()->sum('quantity_ordered');
    }

    public function getReceivedItemsCount(): int
    {
        return $this->items()->sum('quantity_received');
    }

    public function getReceiveProgress(): float
    {
        $totalOrdered = $this->getTotalItemsCount();
        if ($totalOrdered === 0) {
            return 0;
        }
        
        return ($this->getReceivedItemsCount() / $totalOrdered) * 100;
    }

    public function updateStatus(): void
    {
        $totalOrdered = $this->getTotalItemsCount();
        $totalReceived = $this->getReceivedItemsCount();

        if ($totalReceived === 0) {
            $this->status = 'ordered';
        } elseif ($totalReceived >= $totalOrdered) {
            $this->status = 'received';
        } else {
            $this->status = 'partial_received';
        }

        $this->save();
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeBySupplier($query, $supplierId)
    {
        return $query->where('supplier_id', $supplierId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('purchase_date', [$startDate, $endDate]);
    }

    // أنواع الحالات
    public static function getStatuses(): array
    {
        return [
            'pending' => 'في الانتظار',
            'ordered' => 'تم الطلب',
            'partial_received' => 'استلام جزئي',
            'received' => 'تم الاستلام',
            'cancelled' => 'ملغي'
        ];
    }

    // إنشاء رقم طلب شراء تلقائي
    public static function generatePurchaseNumber($companyId): string
    {
        $prefix = 'PO';
        $year = date('Y');
        $month = date('m');
        
        $lastPurchase = static::where('company_id', $companyId)
                             ->whereYear('created_at', $year)
                             ->whereMonth('created_at', $month)
                             ->orderBy('id', 'desc')
                             ->first();

        $sequence = $lastPurchase ? 
                   (int) substr($lastPurchase->purchase_number, -4) + 1 : 1;

        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
