<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class TapPaymentService
{
    private string $secretKey;
    private string $publicKey;
    private string $baseUrl;
    private bool $isProduction;

    public function __construct()
    {
        $this->secretKey = config('services.tap.secret_key');
        $this->publicKey = config('services.tap.public_key');
        $this->isProduction = config('services.tap.environment') === 'production';
        $this->baseUrl = $this->isProduction 
            ? 'https://api.tap.company/v2' 
            : 'https://api.tap.company/v2';
    }

    /**
     * إنشاء دفعة جديدة
     */
    public function createCharge(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/charges', [
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'SAR',
                'threeDSecure' => true,
                'save_card' => false,
                'description' => $data['description'] ?? 'Payment for Wisaq POS',
                'statement_descriptor' => 'WISAQ POS',
                'metadata' => [
                    'company_id' => $data['company_id'] ?? null,
                    'subscription_id' => $data['subscription_id'] ?? null,
                    'order_id' => $data['order_id'] ?? null,
                ],
                'reference' => [
                    'transaction' => $data['reference'] ?? uniqid('tap_'),
                    'order' => $data['order_reference'] ?? null,
                ],
                'receipt' => [
                    'email' => $data['receipt_email'] ?? false,
                    'sms' => $data['receipt_sms'] ?? false,
                ],
                'customer' => [
                    'first_name' => $data['customer']['first_name'] ?? '',
                    'middle_name' => $data['customer']['middle_name'] ?? '',
                    'last_name' => $data['customer']['last_name'] ?? '',
                    'email' => $data['customer']['email'] ?? '',
                    'phone' => [
                        'country_code' => $data['customer']['phone']['country_code'] ?? '966',
                        'number' => $data['customer']['phone']['number'] ?? '',
                    ],
                ],
                'source' => [
                    'id' => 'src_card',
                ],
                'post' => [
                    'url' => $data['post_url'] ?? config('app.url') . '/api/v1/payments/tap/callback',
                ],
                'redirect' => [
                    'url' => $data['redirect_url'] ?? config('app.url') . '/payment/success',
                ],
            ]);

            if ($response->successful()) {
                $result = $response->json();
                
                Log::info('Tap payment charge created', [
                    'charge_id' => $result['id'],
                    'amount' => $data['amount'],
                    'currency' => $data['currency'] ?? 'SAR',
                    'company_id' => $data['company_id'] ?? null,
                ]);

                return [
                    'success' => true,
                    'data' => $result,
                    'payment_url' => $result['transaction']['url'] ?? null,
                ];
            }

            Log::error('Tap payment charge failed', [
                'response' => $response->json(),
                'status' => $response->status(),
            ]);

            return [
                'success' => false,
                'error' => $response->json()['errors'] ?? 'Payment creation failed',
                'status_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            Log::error('Tap payment service error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * استرداد معلومات الدفعة
     */
    public function retrieveCharge(string $chargeId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
            ])->get($this->baseUrl . '/charges/' . $chargeId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json(),
                ];
            }

            return [
                'success' => false,
                'error' => $response->json()['errors'] ?? 'Failed to retrieve charge',
                'status_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            Log::error('Tap retrieve charge error', [
                'charge_id' => $chargeId,
                'message' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to retrieve charge: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * استرداد دفعة
     */
    public function refundCharge(string $chargeId, float $amount, string $reason = ''): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secretKey,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl . '/refunds', [
                'charge_id' => $chargeId,
                'amount' => $amount,
                'currency' => 'SAR',
                'description' => $reason ?: 'Refund from Wisaq POS',
                'reason' => 'requested_by_customer',
                'reference' => [
                    'merchant' => uniqid('refund_'),
                ],
                'metadata' => [
                    'refund_reason' => $reason,
                    'refunded_at' => now()->toISOString(),
                ],
            ]);

            if ($response->successful()) {
                $result = $response->json();
                
                Log::info('Tap payment refund created', [
                    'refund_id' => $result['id'],
                    'charge_id' => $chargeId,
                    'amount' => $amount,
                    'reason' => $reason,
                ]);

                return [
                    'success' => true,
                    'data' => $result,
                ];
            }

            Log::error('Tap payment refund failed', [
                'charge_id' => $chargeId,
                'amount' => $amount,
                'response' => $response->json(),
            ]);

            return [
                'success' => false,
                'error' => $response->json()['errors'] ?? 'Refund failed',
                'status_code' => $response->status(),
            ];

        } catch (\Exception $e) {
            Log::error('Tap refund service error', [
                'charge_id' => $chargeId,
                'message' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Refund service error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * التحقق من صحة webhook
     */
    public function verifyWebhook(array $payload, string $signature): bool
    {
        $webhookSecret = config('services.tap.webhook_secret');
        $expectedSignature = hash_hmac('sha256', json_encode($payload), $webhookSecret);
        
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * معالجة webhook من Tap
     */
    public function handleWebhook(array $payload): array
    {
        try {
            $eventType = $payload['event'] ?? '';
            $chargeData = $payload['data'] ?? [];

            Log::info('Tap webhook received', [
                'event' => $eventType,
                'charge_id' => $chargeData['id'] ?? null,
            ]);

            switch ($eventType) {
                case 'charge.created':
                    return $this->handleChargeCreated($chargeData);
                
                case 'charge.updated':
                    return $this->handleChargeUpdated($chargeData);
                
                case 'charge.failed':
                    return $this->handleChargeFailed($chargeData);
                
                case 'refund.created':
                    return $this->handleRefundCreated($chargeData);
                
                default:
                    Log::warning('Unknown Tap webhook event', ['event' => $eventType]);
                    return ['success' => true, 'message' => 'Event ignored'];
            }

        } catch (\Exception $e) {
            Log::error('Tap webhook processing error', [
                'message' => $e->getMessage(),
                'payload' => $payload,
            ]);

            return [
                'success' => false,
                'error' => 'Webhook processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * معالجة إنشاء دفعة
     */
    private function handleChargeCreated(array $chargeData): array
    {
        // سيتم تطبيق هذا مع Payment model
        return ['success' => true, 'message' => 'Charge created processed'];
    }

    /**
     * معالجة تحديث دفعة
     */
    private function handleChargeUpdated(array $chargeData): array
    {
        // سيتم تطبيق هذا مع Payment model
        return ['success' => true, 'message' => 'Charge updated processed'];
    }

    /**
     * معالجة فشل دفعة
     */
    private function handleChargeFailed(array $chargeData): array
    {
        // سيتم تطبيق هذا مع Payment model
        return ['success' => true, 'message' => 'Charge failed processed'];
    }

    /**
     * معالجة إنشاء استرداد
     */
    private function handleRefundCreated(array $chargeData): array
    {
        // سيتم تطبيق هذا مع Payment model
        return ['success' => true, 'message' => 'Refund created processed'];
    }

    /**
     * الحصول على Public Key للـ Frontend
     */
    public function getPublicKey(): string
    {
        return $this->publicKey;
    }

    /**
     * التحقق من إعدادات Tap
     */
    public function validateConfiguration(): array
    {
        $errors = [];

        if (empty($this->secretKey)) {
            $errors[] = 'Tap secret key is not configured';
        }

        if (empty($this->publicKey)) {
            $errors[] = 'Tap public key is not configured';
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
            'environment' => $this->isProduction ? 'production' : 'sandbox',
        ];
    }
}
