<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('name');
            $table->string('name_en');
            $table->string('symbol'); // الرمز (كجم، لتر، قطعة)
            $table->string('symbol_en');
            $table->enum('type', ['weight', 'volume', 'length', 'area', 'count', 'time'])->default('count');
            $table->decimal('base_unit_conversion', 10, 4)->default(1); // التحويل للوحدة الأساسية
            $table->boolean('is_base_unit')->default(false); // وحدة أساسية
            $table->boolean('allow_decimal')->default(false); // السماح بالكسور العشرية
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            
            $table->index(['company_id', 'is_active']);
            $table->index(['type', 'is_active']);
        });

        // جدول تحويل الوحدات
        Schema::create('unit_conversions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('from_unit_id');
            $table->unsignedBigInteger('to_unit_id');
            $table->decimal('conversion_factor', 10, 4); // معامل التحويل
            $table->timestamps();

            $table->foreign('from_unit_id')->references('id')->on('units')->onDelete('cascade');
            $table->foreign('to_unit_id')->references('id')->on('units')->onDelete('cascade');
            
            $table->unique(['from_unit_id', 'to_unit_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_conversions');
        Schema::dropIfExists('units');
    }
};
