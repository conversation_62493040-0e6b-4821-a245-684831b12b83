<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->string('code')->nullable(); // كود الخصم
            $table->enum('type', ['percentage', 'fixed_amount']); // نسبة مئوية أو مبلغ ثابت
            $table->decimal('value', 10, 2); // قيمة الخصم
            $table->decimal('min_amount', 10, 2)->nullable(); // الحد الأدنى للمبلغ
            $table->decimal('max_discount', 10, 2)->nullable(); // الحد الأقصى للخصم
            $table->enum('applies_to', ['all', 'categories', 'products', 'customers']); // ينطبق على
            $table->json('applies_to_ids')->nullable(); // معرفات الفئات/المنتجات/العملاء
            $table->integer('usage_limit')->nullable(); // حد الاستخدام
            $table->integer('used_count')->default(0); // عدد مرات الاستخدام
            $table->datetime('starts_at')->nullable();
            $table->datetime('expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            
            $table->index(['company_id', 'is_active']);
            $table->index(['code', 'company_id']);
            $table->index(['starts_at', 'expires_at']);
        });

        // جدول استخدام الخصومات
        Schema::create('discount_usages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('discount_id');
            $table->unsignedBigInteger('sale_id');
            $table->unsignedBigInteger('user_id');
            $table->decimal('discount_amount', 10, 2);
            $table->timestamps();

            $table->foreign('discount_id')->references('id')->on('discounts')->onDelete('cascade');
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discount_usages');
        Schema::dropIfExists('discounts');
    }
};
