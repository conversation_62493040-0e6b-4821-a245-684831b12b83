<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\InventoryLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    /**
     * عرض تقرير المخزون
     */
    public function index(Request $request): JsonResponse
    {
        $query = Product::with(['category', 'company'])
                        ->where('company_id', auth()->user()->company_id)
                        ->where('track_stock', true);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الفئة
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // فلترة حسب حالة المخزون
        if ($request->has('stock_status')) {
            switch ($request->stock_status) {
                case 'low':
                    $query->whereRaw('stock_quantity <= min_stock_level');
                    break;
                case 'out':
                    $query->where('stock_quantity', '<=', 0);
                    break;
                case 'normal':
                    $query->whereRaw('stock_quantity > min_stock_level');
                    break;
            }
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // التصفح
        $perPage = $request->get('per_page', 15);
        $products = $query->paginate($perPage);

        // إحصائيات المخزون
        $stats = [
            'total_products' => Product::where('company_id', auth()->user()->company_id)
                                     ->where('track_stock', true)
                                     ->where('is_active', true)
                                     ->count(),
            'low_stock_count' => Product::where('company_id', auth()->user()->company_id)
                                      ->where('track_stock', true)
                                      ->where('is_active', true)
                                      ->whereRaw('stock_quantity <= min_stock_level')
                                      ->count(),
            'out_of_stock_count' => Product::where('company_id', auth()->user()->company_id)
                                         ->where('track_stock', true)
                                         ->where('is_active', true)
                                         ->where('stock_quantity', '<=', 0)
                                         ->count(),
            'total_stock_value' => Product::where('company_id', auth()->user()->company_id)
                                        ->where('track_stock', true)
                                        ->where('is_active', true)
                                        ->sum(DB::raw('stock_quantity * cost_price'))
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'products' => $products,
                'stats' => $stats
            ]
        ]);
    }

    /**
     * تعديل كمية المخزون
     */
    public function adjustStock(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer|exists:products,id',
            'adjustment_type' => 'required|in:in,out,set',
            'quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $product = Product::where('company_id', auth()->user()->company_id)
                         ->findOrFail($request->product_id);

        if (!$product->track_stock) {
            return response()->json([
                'success' => false,
                'message' => 'هذا المنتج لا يتم تتبع مخزونه'
            ], 400);
        }

        $oldQuantity = $product->stock_quantity;
        $newQuantity = $oldQuantity;

        switch ($request->adjustment_type) {
            case 'in':
                $newQuantity = $oldQuantity + $request->quantity;
                $type = 'adjustment_in';
                break;
            case 'out':
                $newQuantity = max(0, $oldQuantity - $request->quantity);
                $type = 'adjustment_out';
                break;
            case 'set':
                $newQuantity = $request->quantity;
                $type = 'adjustment_set';
                break;
        }

        // تحديث المخزون
        $product->update(['stock_quantity' => $newQuantity]);

        // تسجيل حركة المخزون
        InventoryLog::create([
            'product_id' => $product->id,
            'type' => $type,
            'quantity_before' => $oldQuantity,
            'quantity_changed' => $request->adjustment_type === 'set' 
                                ? ($newQuantity - $oldQuantity) 
                                : ($request->adjustment_type === 'in' ? $request->quantity : -$request->quantity),
            'quantity_after' => $newQuantity,
            'reason' => $request->reason,
            'notes' => $request->notes,
            'user_id' => auth()->id(),
            'branch_id' => auth()->user()->branch_id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تعديل المخزون بنجاح',
            'data' => [
                'product' => $product->fresh(),
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity
            ]
        ]);
    }

    /**
     * تعديل كميات متعددة
     */
    public function bulkAdjustStock(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'adjustments' => 'required|array|min:1',
            'adjustments.*.product_id' => 'required|integer|exists:products,id',
            'adjustments.*.adjustment_type' => 'required|in:in,out,set',
            'adjustments.*.quantity' => 'required|integer|min:1',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $results = [];
        $companyId = auth()->user()->company_id;

        DB::beginTransaction();
        try {
            foreach ($request->adjustments as $adjustment) {
                $product = Product::where('company_id', $companyId)
                                 ->findOrFail($adjustment['product_id']);

                if (!$product->track_stock) {
                    continue;
                }

                $oldQuantity = $product->stock_quantity;
                $newQuantity = $oldQuantity;

                switch ($adjustment['adjustment_type']) {
                    case 'in':
                        $newQuantity = $oldQuantity + $adjustment['quantity'];
                        $type = 'adjustment_in';
                        break;
                    case 'out':
                        $newQuantity = max(0, $oldQuantity - $adjustment['quantity']);
                        $type = 'adjustment_out';
                        break;
                    case 'set':
                        $newQuantity = $adjustment['quantity'];
                        $type = 'adjustment_set';
                        break;
                }

                // تحديث المخزون
                $product->update(['stock_quantity' => $newQuantity]);

                // تسجيل حركة المخزون
                InventoryLog::create([
                    'product_id' => $product->id,
                    'type' => $type,
                    'quantity_before' => $oldQuantity,
                    'quantity_changed' => $adjustment['adjustment_type'] === 'set' 
                                        ? ($newQuantity - $oldQuantity) 
                                        : ($adjustment['adjustment_type'] === 'in' ? $adjustment['quantity'] : -$adjustment['quantity']),
                    'quantity_after' => $newQuantity,
                    'reason' => $request->reason,
                    'notes' => $request->notes,
                    'user_id' => auth()->id(),
                    'branch_id' => auth()->user()->branch_id
                ]);

                $results[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'old_quantity' => $oldQuantity,
                    'new_quantity' => $newQuantity
                ];
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تعديل المخزون بنجاح',
                'data' => $results
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعديل المخزون'
            ], 500);
        }
    }

    /**
     * سجل حركة المخزون
     */
    public function logs(Request $request): JsonResponse
    {
        $query = InventoryLog::with(['product', 'user', 'branch'])
                            ->whereHas('product', function($q) {
                                $q->where('company_id', auth()->user()->company_id);
                            });

        // فلترة حسب المنتج
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // فلترة حسب النوع
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب المستخدم
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // فلترة حسب الفرع
        if ($request->has('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // فلترة حسب التاريخ
        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // الترتيب
        $query->orderBy('created_at', 'desc');

        // التصفح
        $perPage = $request->get('per_page', 15);
        $logs = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $logs
        ]);
    }

    /**
     * المنتجات منخفضة المخزون
     */
    public function lowStock(Request $request): JsonResponse
    {
        $products = Product::with(['category'])
                          ->where('company_id', auth()->user()->company_id)
                          ->where('track_stock', true)
                          ->where('is_active', true)
                          ->whereRaw('stock_quantity <= min_stock_level')
                          ->orderBy('stock_quantity', 'asc')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * المنتجات نافدة المخزون
     */
    public function outOfStock(Request $request): JsonResponse
    {
        $products = Product::with(['category'])
                          ->where('company_id', auth()->user()->company_id)
                          ->where('track_stock', true)
                          ->where('is_active', true)
                          ->where('stock_quantity', '<=', 0)
                          ->orderBy('name')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * تقرير قيمة المخزون
     */
    public function stockValue(Request $request): JsonResponse
    {
        $query = Product::where('company_id', auth()->user()->company_id)
                       ->where('track_stock', true)
                       ->where('is_active', true);

        // فلترة حسب الفئة
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        $products = $query->get();

        $totalCostValue = $products->sum(function($product) {
            return $product->stock_quantity * $product->cost_price;
        });

        $totalSellingValue = $products->sum(function($product) {
            return $product->stock_quantity * $product->selling_price;
        });

        $potentialProfit = $totalSellingValue - $totalCostValue;

        return response()->json([
            'success' => true,
            'data' => [
                'total_cost_value' => $totalCostValue,
                'total_selling_value' => $totalSellingValue,
                'potential_profit' => $potentialProfit,
                'profit_margin' => $totalCostValue > 0 ? ($potentialProfit / $totalCostValue) * 100 : 0,
                'products_count' => $products->count(),
                'total_quantity' => $products->sum('stock_quantity')
            ]
        ]);
    }
}
