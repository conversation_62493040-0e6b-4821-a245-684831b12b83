<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('code')->unique(); // كود الكوبون
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed_amount', 'free_shipping']); // نوع الخصم
            $table->decimal('value', 10, 2); // قيمة الخصم
            $table->decimal('minimum_amount', 10, 2)->nullable(); // الحد الأدنى للمبلغ
            $table->decimal('maximum_discount', 10, 2)->nullable(); // الحد الأقصى للخصم
            $table->integer('usage_limit')->nullable(); // حد الاستخدام الإجمالي
            $table->integer('usage_limit_per_customer')->nullable(); // حد الاستخدام لكل عميل
            $table->integer('used_count')->default(0); // عدد مرات الاستخدام
            $table->datetime('starts_at');
            $table->datetime('expires_at');
            $table->json('applicable_products')->nullable(); // المنتجات المطبقة عليها
            $table->json('applicable_categories')->nullable(); // الفئات المطبقة عليها
            $table->json('applicable_customers')->nullable(); // العملاء المطبقة عليهم
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            
            $table->index(['company_id', 'is_active']);
            $table->index(['code', 'is_active']);
            $table->index(['starts_at', 'expires_at']);
        });

        // جدول استخدام الكوبونات
        Schema::create('coupon_usages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('coupon_id');
            $table->unsignedBigInteger('sale_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->decimal('discount_amount', 10, 2);
            $table->timestamps();

            $table->foreign('coupon_id')->references('id')->on('coupons')->onDelete('cascade');
            $table->foreign('sale_id')->references('id')->on('sales')->onDelete('cascade');
            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupon_usages');
        Schema::dropIfExists('coupons');
    }
};
