<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request): JsonResponse
    {
        $query = Customer::with(['company'])
                         ->where('company_id', auth()->user()->company_id);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('customer_code', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب الفئة
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // التصفح
        $perPage = $request->get('per_page', 15);
        $customers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $customers
        ]);
    }

    /**
     * إنشاء عميل جديد
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => 'nullable|string|max:20',
            'phone2' => 'nullable|string|max:20',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:2',
            'postal_code' => 'nullable|string|max:20',
            'type' => 'required|in:individual,company',
            'category' => 'nullable|in:regular,vip,wholesale,retail',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'birth_date' => 'nullable|date',
            'registration_date' => 'nullable|date',
            'contact_person' => 'nullable|string|max:255',
            'website' => 'nullable|url',
            'social_media' => 'nullable|array',
            'send_notifications' => 'nullable|boolean',
            'notes' => 'nullable|string',
            'custom_fields' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        // إنشاء رمز العميل تلقائياً
        $customerCode = $this->generateCustomerCode();

        $customer = Customer::create(array_merge($validator->validated(), [
            'company_id' => auth()->user()->company_id,
            'customer_code' => $customerCode
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء العميل بنجاح',
            'data' => $customer->load('company')
        ], 201);
    }

    /**
     * عرض تفاصيل عميل
     */
    public function show($id): JsonResponse
    {
        $customer = Customer::with(['company'])
                           ->where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // إحصائيات العميل
        $stats = [
            'total_purchases' => $customer->sales()->sum('total_amount'),
            'total_orders' => $customer->sales()->count(),
            'last_purchase' => $customer->sales()->latest()->first()?->created_at,
            'average_order_value' => $customer->sales()->avg('total_amount') ?? 0
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => $customer,
                'stats' => $stats
            ]
        ]);
    }

    /**
     * تحديث بيانات عميل
     */
    public function update(Request $request, $id): JsonResponse
    {
        $customer = Customer::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:customers,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'phone2' => 'nullable|string|max:20',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:2',
            'postal_code' => 'nullable|string|max:20',
            'type' => 'required|in:individual,company',
            'category' => 'nullable|in:regular,vip,wholesale,retail',
            'credit_limit' => 'nullable|numeric|min:0',
            'credit_days' => 'nullable|integer|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'birth_date' => 'nullable|date',
            'registration_date' => 'nullable|date',
            'contact_person' => 'nullable|string|max:255',
            'website' => 'nullable|url',
            'social_media' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'send_notifications' => 'nullable|boolean',
            'notes' => 'nullable|string',
            'custom_fields' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $customer->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث بيانات العميل بنجاح',
            'data' => $customer->load('company')
        ]);
    }

    /**
     * حذف عميل
     */
    public function destroy($id): JsonResponse
    {
        $customer = Customer::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // التحقق من وجود مبيعات للعميل
        if ($customer->sales()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف العميل لوجود مبيعات مرتبطة به'
            ], 400);
        }

        $customer->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف العميل بنجاح'
        ]);
    }

    /**
     * البحث في العملاء
     */
    public function search(Request $request): JsonResponse
    {
        $term = $request->get('term', '');
        
        $customers = Customer::where('company_id', auth()->user()->company_id)
                            ->where('is_active', true)
                            ->where(function($query) use ($term) {
                                $query->where('name', 'like', "%{$term}%")
                                      ->orWhere('email', 'like', "%{$term}%")
                                      ->orWhere('phone', 'like', "%{$term}%")
                                      ->orWhere('customer_code', 'like', "%{$term}%");
                            })
                            ->limit(10)
                            ->get(['id', 'name', 'email', 'phone', 'customer_code']);

        return response()->json([
            'success' => true,
            'data' => $customers
        ]);
    }

    /**
     * إنشاء رمز عميل تلقائي
     */
    private function generateCustomerCode(): string
    {
        $companyId = auth()->user()->company_id;
        $prefix = 'CUS';
        
        do {
            $number = str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $code = $prefix . $number;
        } while (Customer::where('company_id', $companyId)->where('customer_code', $code)->exists());

        return $code;
    }

    /**
     * تصدير العملاء
     */
    public function export(Request $request): JsonResponse
    {
        // سيتم تطبيق هذه الوظيفة لاحقاً
        return response()->json([
            'success' => false,
            'message' => 'وظيفة التصدير قيد التطوير'
        ], 501);
    }

    /**
     * استيراد العملاء
     */
    public function import(Request $request): JsonResponse
    {
        // سيتم تطبيق هذه الوظيفة لاحقاً
        return response()->json([
            'success' => false,
            'message' => 'وظيفة الاستيراد قيد التطوير'
        ], 501);
    }
}
