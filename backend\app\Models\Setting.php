<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'name',
        'name_en',
        'description',
        'description_en',
        'is_public',
        'is_editable',
        'company_id',
        'branch_id'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_editable' => 'boolean'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    // Accessors
    public function getValueAttribute($value)
    {
        return match($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'json', 'array' => json_decode($value, true),
            default => $value
        };
    }

    // Mutators
    public function setValueAttribute($value)
    {
        $this->attributes['value'] = match($this->type) {
            'boolean' => $value ? '1' : '0',
            'json', 'array' => json_encode($value),
            default => (string) $value
        };
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeEditable($query)
    {
        return $query->where('is_editable', true);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeGlobal($query)
    {
        return $query->whereNull('company_id')->whereNull('branch_id');
    }

    // الدوال المساعدة
    public static function get($key, $default = null, $companyId = null, $branchId = null)
    {
        $query = static::where('key', $key);
        
        if ($branchId) {
            $query->where('branch_id', $branchId);
        } elseif ($companyId) {
            $query->where('company_id', $companyId)->whereNull('branch_id');
        } else {
            $query->whereNull('company_id')->whereNull('branch_id');
        }
        
        $setting = $query->first();
        return $setting ? $setting->value : $default;
    }

    public static function set($key, $value, $companyId = null, $branchId = null)
    {
        return static::updateOrCreate(
            [
                'key' => $key,
                'company_id' => $companyId,
                'branch_id' => $branchId
            ],
            ['value' => $value]
        );
    }

    public static function getDefaultSettings(): array
    {
        return [
            // إعدادات عامة
            ['key' => 'app_name', 'value' => 'Wisaq POS', 'type' => 'string', 'group' => 'general', 'name' => 'اسم التطبيق'],
            ['key' => 'app_logo', 'value' => '', 'type' => 'string', 'group' => 'general', 'name' => 'شعار التطبيق'],
            ['key' => 'default_language', 'value' => 'ar', 'type' => 'string', 'group' => 'general', 'name' => 'اللغة الافتراضية'],
            ['key' => 'default_currency', 'value' => 'SAR', 'type' => 'string', 'group' => 'general', 'name' => 'العملة الافتراضية'],
            ['key' => 'default_timezone', 'value' => 'Asia/Riyadh', 'type' => 'string', 'group' => 'general', 'name' => 'المنطقة الزمنية'],
            
            // إعدادات الضرائب
            ['key' => 'vat_rate', 'value' => '15', 'type' => 'float', 'group' => 'tax', 'name' => 'معدل ضريبة القيمة المضافة'],
            ['key' => 'tax_number', 'value' => '', 'type' => 'string', 'group' => 'tax', 'name' => 'الرقم الضريبي'],
            ['key' => 'include_tax_in_price', 'value' => '1', 'type' => 'boolean', 'group' => 'tax', 'name' => 'تضمين الضريبة في السعر'],
            
            // إعدادات المبيعات
            ['key' => 'auto_print_receipt', 'value' => '1', 'type' => 'boolean', 'group' => 'sales', 'name' => 'طباعة الإيصال تلقائياً'],
            ['key' => 'allow_negative_stock', 'value' => '0', 'type' => 'boolean', 'group' => 'sales', 'name' => 'السماح بالبيع عند نفاد المخزون'],
            ['key' => 'default_payment_method', 'value' => 'cash', 'type' => 'string', 'group' => 'sales', 'name' => 'طريقة الدفع الافتراضية'],
            
            // إعدادات المخزون
            ['key' => 'low_stock_alert', 'value' => '1', 'type' => 'boolean', 'group' => 'inventory', 'name' => 'تنبيه المخزون المنخفض'],
            ['key' => 'auto_generate_sku', 'value' => '1', 'type' => 'boolean', 'group' => 'inventory', 'name' => 'إنشاء رمز المنتج تلقائياً'],
            
            // إعدادات الإيصالات
            ['key' => 'receipt_header', 'value' => '', 'type' => 'text', 'group' => 'receipt', 'name' => 'رأس الإيصال'],
            ['key' => 'receipt_footer', 'value' => 'شكراً لزيارتكم', 'type' => 'text', 'group' => 'receipt', 'name' => 'ذيل الإيصال'],
            ['key' => 'receipt_width', 'value' => '80', 'type' => 'integer', 'group' => 'receipt', 'name' => 'عرض الإيصال (مم)'],
            
            // إعدادات النسخ الاحتياطية
            ['key' => 'auto_backup', 'value' => '1', 'type' => 'boolean', 'group' => 'backup', 'name' => 'النسخ الاحتياطية التلقائية'],
            ['key' => 'backup_frequency', 'value' => 'daily', 'type' => 'string', 'group' => 'backup', 'name' => 'تكرار النسخ الاحتياطية'],
            ['key' => 'backup_retention_days', 'value' => '30', 'type' => 'integer', 'group' => 'backup', 'name' => 'مدة الاحتفاظ بالنسخ (أيام)']
        ];
    }
}
