<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    protected $fillable = [
        'company_id',
        'customer_code',
        'name',
        'name_en',
        'email',
        'phone',
        'phone2',
        'tax_number',
        'commercial_register',
        'address',
        'address_en',
        'city',
        'state',
        'country',
        'postal_code',
        'type',
        'category',
        'credit_limit',
        'current_balance',
        'credit_days',
        'discount_rate',
        'birth_date',
        'registration_date',
        'contact_person',
        'website',
        'social_media',
        'is_active',
        'send_notifications',
        'notes',
        'custom_fields'
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'birth_date' => 'date',
        'registration_date' => 'date',
        'social_media' => 'array',
        'custom_fields' => 'array',
        'is_active' => 'boolean',
        'send_notifications' => 'boolean'
    ];

    // العلاقات
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    // الدوال المساعدة
    public function getTotalPurchases(): float
    {
        return $this->sales()->sum('total_amount');
    }

    public function getTotalOrders(): int
    {
        return $this->sales()->count();
    }

    public function getLastPurchaseDate()
    {
        return $this->sales()->latest()->first()?->sale_date;
    }

    public function getAverageOrderValue(): float
    {
        return $this->sales()->avg('total_amount') ?? 0;
    }

    public function isVip(): bool
    {
        return $this->category === 'vip';
    }

    public function isWholesale(): bool
    {
        return $this->category === 'wholesale';
    }

    public function hasCredit(): bool
    {
        return $this->credit_limit > 0;
    }

    public function getAvailableCredit(): float
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    public function canPurchaseOnCredit($amount): bool
    {
        return $this->hasCredit() && $this->getAvailableCredit() >= $amount;
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    public function scopeVip($query)
    {
        return $query->where('category', 'vip');
    }

    public function scopeWholesale($query)
    {
        return $query->where('category', 'wholesale');
    }

    public function scopeWithCredit($query)
    {
        return $query->where('credit_limit', '>', 0);
    }

    public function scopeSearch($query, $term)
    {
        return $query->where(function($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('name_en', 'like', "%{$term}%")
              ->orWhere('customer_code', 'like', "%{$term}%")
              ->orWhere('email', 'like', "%{$term}%")
              ->orWhere('phone', 'like', "%{$term}%");
        });
    }

    // أنواع العملاء
    public static function getTypes(): array
    {
        return [
            'individual' => 'فرد',
            'company' => 'شركة'
        ];
    }

    // فئات العملاء
    public static function getCategories(): array
    {
        return [
            'regular' => 'عادي',
            'vip' => 'مميز',
            'wholesale' => 'جملة',
            'retail' => 'تجزئة'
        ];
    }
}
