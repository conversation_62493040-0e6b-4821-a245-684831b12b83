APP_NAME="Wisaq POS System"
APP_ENV=production
APP_KEY=base64:your-production-app-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

APP_LOCALE=ar
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=ar_SA

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=wisaq_pos
DB_USERNAME=wisaq_user
DB_PASSWORD=your-secure-database-password

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=your-domain.com

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=redis
CACHE_PREFIX=wisaq_pos

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Wisaq POS System"

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_TTL=60
JWT_REFRESH_TTL=20160

# Tap Payment Gateway (Production)
TAP_SECRET_KEY=sk_live_your_live_secret_key
TAP_PUBLIC_KEY=pk_live_your_live_public_key
TAP_WEBHOOK_SECRET=your_production_webhook_secret

# Application Settings
DEFAULT_CURRENCY=SAR
DEFAULT_TAX_RATE=15
DEFAULT_TIMEZONE=Asia/Riyadh

# File Upload Settings
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,xlsx,csv

# Security Settings
SANCTUM_STATEFUL_DOMAINS=your-domain.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=strict

# Backup Settings
BACKUP_DISK=s3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-backup-bucket

# Monitoring
SENTRY_LARAVEL_DSN=your-sentry-dsn-here

VITE_APP_NAME="${APP_NAME}"
