<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Company extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'email',
        'phone',
        'tax_number',
        'commercial_register',
        'address',
        'city',
        'country',
        'logo',
        'subscription_plan',
        'subscription_status',
        'branches_count',
        'max_branches',
        'monthly_fee',
        'subscription_starts_at',
        'subscription_expires_at',
        'trial_ends_at',
        'settings',
        'is_active'
    ];

    protected $casts = [
        'settings' => 'array',
        'subscription_starts_at' => 'date',
        'subscription_expires_at' => 'date',
        'trial_ends_at' => 'date',
        'monthly_fee' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    // العلاقات
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function branches(): HasMany
    {
        return $this->hasMany(Branch::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    public function categories(): HasMany
    {
        return $this->hasMany(Category::class);
    }

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function suppliers(): HasMany
    {
        return $this->hasMany(Supplier::class);
    }

    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    public function currentSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')->latest();
    }

    public function settings(): HasMany
    {
        return $this->hasMany(Setting::class);
    }

    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    public function discounts(): HasMany
    {
        return $this->hasMany(Discount::class);
    }

    public function taxes(): HasMany
    {
        return $this->hasMany(Tax::class);
    }

    public function activityLogs(): HasMany
    {
        return $this->hasMany(ActivityLog::class);
    }

    // الدوال المساعدة
    public function isActive(): bool
    {
        return $this->is_active &&
               $this->subscription_status === 'active' &&
               (!$this->subscription_expires_at || $this->subscription_expires_at > now());
    }

    public function isOnTrial(): bool
    {
        return $this->subscription_status === 'trial' &&
               (!$this->trial_ends_at || $this->trial_ends_at > now());
    }

    public function isTrialExpired(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    public function canAddBranch(): bool
    {
        return $this->branches_count < $this->max_branches;
    }

    public function getRemainingTrialDays(): int
    {
        if (!$this->trial_ends_at) {
            return 0;
        }

        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    public function getSetting($key, $default = null)
    {
        return Setting::get($key, $default, $this->id);
    }

    public function setSetting($key, $value)
    {
        return Setting::set($key, $value, $this->id);
    }

    public function getDefaultTax()
    {
        return $this->taxes()->where('is_default', true)->where('is_active', true)->first();
    }

    public function getActiveDiscounts()
    {
        return $this->discounts()->active()->get();
    }

    public function getTotalSales($startDate = null, $endDate = null)
    {
        $query = $this->sales();

        if ($startDate) {
            $query->where('sale_date', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('sale_date', '<=', $endDate);
        }

        return $query->sum('total_amount');
    }

    public function getTotalProducts()
    {
        return $this->products()->where('is_active', true)->count();
    }

    public function getLowStockProducts()
    {
        return $this->products()
                    ->whereRaw('stock_quantity <= min_stock_level')
                    ->where('is_active', true)
                    ->get();
    }

    public function getTopSellingProducts($limit = 10)
    {
        return $this->products()
                    ->join('sale_items', 'products.id', '=', 'sale_items.product_id')
                    ->selectRaw('products.*, SUM(sale_items.quantity) as total_sold')
                    ->groupBy('products.id')
                    ->orderByDesc('total_sold')
                    ->limit($limit)
                    ->get();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOnTrial($query)
    {
        return $query->where('subscription_status', 'trial')
                    ->where('trial_ends_at', '>', now());
    }

    public function scopeExpiringSoon($query, $days = 7)
    {
        return $query->where('subscription_expires_at', '<=', now()->addDays($days))
                    ->where('subscription_expires_at', '>', now());
    }
}
