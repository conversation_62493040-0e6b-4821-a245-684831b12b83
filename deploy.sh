#!/bin/bash

# Wisaq POS System Deployment Script
# This script automates the deployment process

set -e

echo "🚀 Starting Wisaq POS System Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/storage/logs
mkdir -p backend/storage/app/public
mkdir -p backend/bootstrap/cache
mkdir -p nginx/conf.d
mkdir -p nginx/ssl

# Set permissions
print_status "Setting permissions..."
chmod -R 775 backend/storage
chmod -R 775 backend/bootstrap/cache

# Copy environment file if it doesn't exist
if [ ! -f backend/.env ]; then
    print_status "Creating environment file..."
    cp backend/.env.example backend/.env
    print_warning "Please update the .env file with your configuration before continuing."
    read -p "Press enter to continue after updating .env file..."
fi

# Build and start containers
print_status "Building and starting Docker containers..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for MySQL to be ready
print_status "Waiting for MySQL to be ready..."
sleep 30

# Run Laravel setup commands
print_status "Running Laravel setup commands..."
docker-compose exec backend php artisan key:generate --force
docker-compose exec backend php artisan migrate --force
docker-compose exec backend php artisan db:seed --force
docker-compose exec backend php artisan storage:link
docker-compose exec backend php artisan config:cache
docker-compose exec backend php artisan route:cache
docker-compose exec backend php artisan view:cache

# Set final permissions
print_status "Setting final permissions..."
docker-compose exec backend chown -R www-data:www-data /var/www/html/storage
docker-compose exec backend chown -R www-data:www-data /var/www/html/bootstrap/cache

print_status "✅ Deployment completed successfully!"
print_status "🌐 Frontend is available at: http://localhost:8080"
print_status "🔧 Backend API is available at: http://localhost/api"
print_status "📊 Admin Panel: http://localhost:8080 (<EMAIL> / password)"
print_status "💰 Cashier Panel: http://localhost:8080 (<EMAIL> / password)"

echo ""
print_warning "Important Notes:"
echo "1. Update your .env file with production settings"
echo "2. Configure SSL certificates for production"
echo "3. Set up proper backup procedures"
echo "4. Configure Tap Payment API keys"
echo "5. Review security settings"

echo ""
print_status "🎉 Wisaq POS System is now running!"
