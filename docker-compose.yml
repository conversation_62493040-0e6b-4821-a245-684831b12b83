version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: wisaq_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: wisaq_pos
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: wisaq_user
      MYSQL_PASSWORD: wisaq_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - wisaq_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: wisaq_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - wisaq_network

  # PHP-FPM Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: wisaq_backend
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - ./backend:/var/www/html
      - ./backend/storage:/var/www/html/storage
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=wisaq_pos
      - DB_USERNAME=wisaq_user
      - DB_PASSWORD=wisaq_password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - wisaq_network

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: wisaq_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./backend:/var/www/html
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - wisaq_network

  # Frontend (React)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: wisaq_frontend
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./dist:/usr/share/nginx/html
    networks:
      - wisaq_network

volumes:
  mysql_data:
    driver: local

networks:
  wisaq_network:
    driver: bridge
