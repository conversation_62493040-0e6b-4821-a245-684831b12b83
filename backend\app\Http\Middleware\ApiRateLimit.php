<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class ApiRateLimit
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $maxAttempts = '60', string $decayMinutes = '1'): Response
    {
        $key = $this->resolveRequestSignature($request);
        $maxAttempts = (int) $maxAttempts;
        $decayMinutes = (int) $decayMinutes;

        // التحقق من عدد المحاولات
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $maxAttempts) {
            return $this->buildResponse($key, $maxAttempts, $decayMinutes);
        }

        // زيادة عدد المحاولات
        Cache::put($key, $attempts + 1, now()->addMinutes($decayMinutes));

        $response = $next($request);

        // إضافة headers للمعلومات
        return $this->addHeaders(
            $response,
            $maxAttempts,
            $this->calculateRemainingAttempts($key, $maxAttempts),
            $this->getTimeUntilNextRetry($key, $decayMinutes)
        );
    }

    /**
     * إنشاء مفتاح فريد للطلب
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $user = Auth::user();
        
        if ($user) {
            // للمستخدمين المسجلين: استخدام معرف المستخدم
            return 'rate_limit:user:' . $user->id . ':' . $request->getPathInfo();
        }
        
        // للمستخدمين غير المسجلين: استخدام IP
        return 'rate_limit:ip:' . $request->ip() . ':' . $request->getPathInfo();
    }

    /**
     * حساب المحاولات المتبقية
     */
    protected function calculateRemainingAttempts(string $key, int $maxAttempts): int
    {
        $attempts = Cache::get($key, 0);
        return max(0, $maxAttempts - $attempts);
    }

    /**
     * الحصول على الوقت المتبقي للمحاولة التالية
     */
    protected function getTimeUntilNextRetry(string $key, int $decayMinutes): int
    {
        $cacheKey = $key . ':timer';
        $resetTime = Cache::get($cacheKey);
        
        if (!$resetTime) {
            $resetTime = now()->addMinutes($decayMinutes)->timestamp;
            Cache::put($cacheKey, $resetTime, now()->addMinutes($decayMinutes));
        }
        
        return max(0, $resetTime - now()->timestamp);
    }

    /**
     * بناء استجابة تجاوز الحد
     */
    protected function buildResponse(string $key, int $maxAttempts, int $decayMinutes): Response
    {
        $retryAfter = $this->getTimeUntilNextRetry($key, $decayMinutes);
        
        $response = response()->json([
            'success' => false,
            'message' => 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.',
            'error_code' => 'RATE_LIMIT_EXCEEDED',
            'retry_after' => $retryAfter,
            'max_attempts' => $maxAttempts,
            'window_minutes' => $decayMinutes
        ], 429);

        return $this->addHeaders($response, $maxAttempts, 0, $retryAfter);
    }

    /**
     * إضافة headers للاستجابة
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $remainingAttempts, int $retryAfter): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => $remainingAttempts,
            'X-RateLimit-Reset' => now()->addSeconds($retryAfter)->timestamp,
        ]);

        if ($remainingAttempts === 0) {
            $response->headers->set('Retry-After', $retryAfter);
        }

        return $response;
    }

    /**
     * تطبيق حدود مختلفة حسب نوع العملية
     */
    public static function forLogin(): string
    {
        return 'api_rate_limit:5,1'; // 5 محاولات في الدقيقة
    }

    public static function forApi(): string
    {
        return 'api_rate_limit:100,1'; // 100 طلب في الدقيقة
    }

    public static function forSensitive(): string
    {
        return 'api_rate_limit:10,1'; // 10 طلبات في الدقيقة للعمليات الحساسة
    }

    public static function forPublic(): string
    {
        return 'api_rate_limit:30,1'; // 30 طلب في الدقيقة للعمليات العامة
    }
}
