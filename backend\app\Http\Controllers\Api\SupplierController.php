<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SupplierController extends Controller
{
    /**
     * عرض قائمة الموردين
     */
    public function index(Request $request): JsonResponse
    {
        $query = Supplier::with(['company'])
                         ->where('company_id', auth()->user()->company_id);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('supplier_code', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب الفئة
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // فلترة الموردين المفضلين
        if ($request->has('is_preferred')) {
            $query->where('is_preferred', $request->boolean('is_preferred'));
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // التصفح
        $perPage = $request->get('per_page', 15);
        $suppliers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $suppliers
        ]);
    }

    /**
     * إنشاء مورد جديد
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_person_en' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:suppliers,email',
            'phone' => 'nullable|string|max:20',
            'phone2' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:2',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'type' => 'required|in:local,international',
            'category' => 'required|in:manufacturer,distributor,wholesaler,retailer',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
            'certifications' => 'nullable|array',
            'products_categories' => 'nullable|array',
            'is_preferred' => 'nullable|boolean',
            'notes' => 'nullable|string',
            'custom_fields' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        // إنشاء رمز المورد تلقائياً
        $supplierCode = $this->generateSupplierCode();

        $supplier = Supplier::create(array_merge($validator->validated(), [
            'company_id' => auth()->user()->company_id,
            'supplier_code' => $supplierCode
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء المورد بنجاح',
            'data' => $supplier->load('company')
        ], 201);
    }

    /**
     * عرض تفاصيل مورد
     */
    public function show($id): JsonResponse
    {
        $supplier = Supplier::with(['company'])
                           ->where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // إحصائيات المورد
        $stats = [
            'total_purchases' => $supplier->purchases()->sum('total_amount'),
            'total_orders' => $supplier->purchases()->count(),
            'last_purchase' => $supplier->purchases()->latest()->first()?->created_at,
            'average_order_value' => $supplier->purchases()->avg('total_amount') ?? 0,
            'pending_orders' => $supplier->purchases()->where('status', 'pending')->count()
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'supplier' => $supplier,
                'stats' => $stats
            ]
        ]);
    }

    /**
     * تحديث بيانات مورد
     */
    public function update(Request $request, $id): JsonResponse
    {
        $supplier = Supplier::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'contact_person' => 'nullable|string|max:255',
            'contact_person_en' => 'nullable|string|max:255',
            'email' => 'nullable|email|unique:suppliers,email,' . $supplier->id,
            'phone' => 'nullable|string|max:20',
            'phone2' => 'nullable|string|max:20',
            'fax' => 'nullable|string|max:20',
            'tax_number' => 'nullable|string|max:50',
            'commercial_register' => 'nullable|string|max:50',
            'address' => 'nullable|string',
            'address_en' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:2',
            'postal_code' => 'nullable|string|max:20',
            'website' => 'nullable|url',
            'type' => 'required|in:local,international',
            'category' => 'required|in:manufacturer,distributor,wholesaler,retailer',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'bank_name' => 'nullable|string|max:255',
            'bank_account' => 'nullable|string|max:50',
            'iban' => 'nullable|string|max:50',
            'certifications' => 'nullable|array',
            'products_categories' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'is_preferred' => 'nullable|boolean',
            'notes' => 'nullable|string',
            'custom_fields' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $supplier->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث بيانات المورد بنجاح',
            'data' => $supplier->load('company')
        ]);
    }

    /**
     * حذف مورد
     */
    public function destroy($id): JsonResponse
    {
        $supplier = Supplier::where('company_id', auth()->user()->company_id)
                           ->findOrFail($id);

        // التحقق من وجود مشتريات للمورد
        if ($supplier->purchases()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف المورد لوجود مشتريات مرتبطة به'
            ], 400);
        }

        $supplier->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المورد بنجاح'
        ]);
    }

    /**
     * البحث في الموردين
     */
    public function search(Request $request): JsonResponse
    {
        $term = $request->get('term', '');
        
        $suppliers = Supplier::where('company_id', auth()->user()->company_id)
                            ->where('is_active', true)
                            ->where(function($query) use ($term) {
                                $query->where('name', 'like', "%{$term}%")
                                      ->orWhere('email', 'like', "%{$term}%")
                                      ->orWhere('phone', 'like', "%{$term}%")
                                      ->orWhere('supplier_code', 'like', "%{$term}%");
                            })
                            ->limit(10)
                            ->get(['id', 'name', 'email', 'phone', 'supplier_code']);

        return response()->json([
            'success' => true,
            'data' => $suppliers
        ]);
    }

    /**
     * الحصول على الموردين المفضلين
     */
    public function preferred(): JsonResponse
    {
        $suppliers = Supplier::where('company_id', auth()->user()->company_id)
                            ->where('is_active', true)
                            ->where('is_preferred', true)
                            ->get(['id', 'name', 'email', 'phone', 'supplier_code']);

        return response()->json([
            'success' => true,
            'data' => $suppliers
        ]);
    }

    /**
     * إنشاء رمز مورد تلقائي
     */
    private function generateSupplierCode(): string
    {
        $companyId = auth()->user()->company_id;
        $prefix = 'SUP';
        
        do {
            $number = str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
            $code = $prefix . $number;
        } while (Supplier::where('company_id', $companyId)->where('supplier_code', $code)->exists());

        return $code;
    }
}
