<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Services\SecurityService;

class AuthController extends Controller
{
    /**
     * تسجيل الدخول
     */
    public function login(Request $request): JsonResponse
    {
        // التحقق من Rate Limiting
        $ip = $request->ip();
        $email = $request->input('email', '');
        $loginKey = "login_attempts:{$ip}:{$email}";
        $attempts = Cache::get($loginKey, 0);

        if ($attempts >= 5) {
            SecurityService::logSecurityEvent('Too many login attempts', [
                'ip' => $ip,
                'email' => $email,
                'attempts' => $attempts
            ]);

            return response()->json([
                'success' => false,
                'message' => 'تم تجاوز عدد محاولات تسجيل الدخول المسموحة. يرجى المحاولة بعد 15 دقيقة.',
                'retry_after' => 900
            ], 429);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'password' => 'required|string|min:6|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $email = SecurityService::sanitizeInput($request->email);
        $credentials = ['email' => $email, 'password' => $request->password];

        if (!Auth::attempt($credentials)) {
            Cache::put($loginKey, $attempts + 1, now()->addMinutes(15));

            SecurityService::logSecurityEvent('Failed login attempt', [
                'ip' => $ip,
                'email' => $email,
                'attempts' => $attempts + 1
            ]);

            return response()->json([
                'success' => false,
                'message' => 'بيانات الدخول غير صحيحة'
            ], 401);
        }

        $user = Auth::user();

        if (!$user->is_active) {
            Auth::logout();
            return response()->json([
                'success' => false,
                'message' => 'حسابك غير مفعل'
            ], 403);
        }

        if ($user->company && !$user->company->isActive()) {
            Auth::logout();
            return response()->json([
                'success' => false,
                'message' => 'اشتراك الشركة منتهي الصلاحية'
            ], 403);
        }

        $token = $user->createToken('auth_token', ['*'], now()->addHours(24))->plainTextToken;
        Cache::forget($loginKey);
        $user->updateLastLogin();

        Log::info('Successful login', [
            'user_id' => $user->id,
            'email' => $user->email,
            'ip' => $ip,
            'company_id' => $user->company_id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل الدخول بنجاح',
            'data' => [
                'user' => $user->load(['company', 'branch']),
                'token' => $token,
                'token_type' => 'Bearer',
                'expires_at' => now()->addHours(24)->toISOString()
            ]
        ]);
    }

    /**
     * تسجيل الخروج
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();

        // تسجيل عملية تسجيل الخروج
        Log::info('User logout', [
            'user_id' => $user->id,
            'email' => $user->email,
            'ip' => $request->ip(),
            'company_id' => $user->company_id
        ]);

        // حذف token الحالي
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح'
        ]);
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function me(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'permissions' => $user->permissions,
                    'company' => $user->company ? [
                        'id' => $user->company->id,
                        'name' => $user->company->name,
                        'subscription_plan' => $user->company->subscription_plan,
                        'subscription_status' => $user->company->subscription_status,
                    ] : null,
                    'branch' => $user->branch ? [
                        'id' => $user->branch->id,
                        'name' => $user->branch->name,
                    ] : null,
                ]
            ]
        ]);
    }

    /**
     * تحديث كلمة المرور
     */
    public function updatePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'كلمة المرور الحالية غير صحيحة'
            ], 400);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث كلمة المرور بنجاح'
        ]);
    }
}
