<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ActivityLogger
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // تسجيل النشاط للطلبات المهمة فقط
        if ($this->shouldLog($request, $response)) {
            $this->logActivity($request, $response);
        }

        return $response;
    }

    /**
     * تحديد ما إذا كان يجب تسجيل النشاط
     */
    private function shouldLog(Request $request, Response $response): bool
    {
        // عدم تسجيل طلبات GET العادية
        if ($request->isMethod('GET') && !$this->isSensitiveGetRequest($request)) {
            return false;
        }

        // عدم تسجيل الطلبات الناجحة للموارد الثابتة
        if ($response->getStatusCode() === 200 && $this->isStaticResource($request)) {
            return false;
        }

        // تسجيل العمليات المهمة
        return $this->isImportantOperation($request) || 
               $response->getStatusCode() >= 400 || 
               Auth::check();
    }

    /**
     * تحديد ما إذا كان طلب GET حساس
     */
    private function isSensitiveGetRequest(Request $request): bool
    {
        $sensitiveRoutes = [
            'api/v1/me',
            'api/v1/dashboard',
            'api/v1/reports',
            'api/v1/users',
            'api/v1/settings'
        ];

        foreach ($sensitiveRoutes as $route) {
            if (str_contains($request->getPathInfo(), $route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تحديد ما إذا كان الطلب لمورد ثابت
     */
    private function isStaticResource(Request $request): bool
    {
        $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'svg', 'ico', 'woff', 'woff2', 'ttf'];
        $path = $request->getPathInfo();
        
        foreach ($staticExtensions as $ext) {
            if (str_ends_with($path, '.' . $ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تحديد ما إذا كانت العملية مهمة
     */
    private function isImportantOperation(Request $request): bool
    {
        $importantOperations = [
            'POST' => ['login', 'logout', 'sales', 'products', 'customers', 'suppliers'],
            'PUT' => ['products', 'customers', 'suppliers', 'users', 'settings'],
            'PATCH' => ['products', 'customers', 'suppliers', 'users', 'settings'],
            'DELETE' => ['products', 'customers', 'suppliers', 'users', 'sales']
        ];

        $method = $request->getMethod();
        $path = $request->getPathInfo();

        if (!isset($importantOperations[$method])) {
            return false;
        }

        foreach ($importantOperations[$method] as $operation) {
            if (str_contains($path, $operation)) {
                return true;
            }
        }

        return false;
    }

    /**
     * تسجيل النشاط
     */
    private function logActivity(Request $request, Response $response): void
    {
        $user = Auth::user();
        $logData = [
            'timestamp' => now()->toISOString(),
            'method' => $request->getMethod(),
            'url' => $request->fullUrl(),
            'path' => $request->getPathInfo(),
            'status_code' => $response->getStatusCode(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'company_id' => $user?->company_id,
            'branch_id' => $user?->branch_id,
            'request_size' => strlen($request->getContent()),
            'response_size' => strlen($response->getContent()),
            'execution_time' => microtime(true) - LARAVEL_START,
        ];

        // إضافة بيانات الطلب للعمليات المهمة (بدون كلمات المرور)
        if ($this->isImportantOperation($request)) {
            $requestData = $request->all();
            unset($requestData['password'], $requestData['password_confirmation'], $requestData['current_password']);
            $logData['request_data'] = $requestData;
        }

        // إضافة معلومات الخطأ للاستجابات الفاشلة
        if ($response->getStatusCode() >= 400) {
            $logData['error_type'] = $this->getErrorType($response->getStatusCode());
            
            if ($response->getStatusCode() === 422) {
                $content = json_decode($response->getContent(), true);
                if (isset($content['errors'])) {
                    $logData['validation_errors'] = $content['errors'];
                }
            }
        }

        // تسجيل النشاط
        Log::channel('activity')->info('User Activity', $logData);

        // حفظ في قاعدة البيانات للعمليات المهمة
        if ($this->isImportantOperation($request) && $user) {
            $this->saveToDatabase($logData, $user);
        }
    }

    /**
     * تحديد نوع الخطأ
     */
    private function getErrorType(int $statusCode): string
    {
        return match($statusCode) {
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            422 => 'Validation Error',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            503 => 'Service Unavailable',
            default => 'Unknown Error'
        };
    }

    /**
     * حفظ النشاط في قاعدة البيانات
     */
    private function saveToDatabase(array $logData, $user): void
    {
        try {
            // سيتم تطبيق هذا لاحقاً مع ActivityLog model
            // ActivityLog::create([
            //     'user_id' => $user->id,
            //     'company_id' => $user->company_id,
            //     'branch_id' => $user->branch_id,
            //     'action' => $logData['method'] . ' ' . $logData['path'],
            //     'ip_address' => $logData['ip_address'],
            //     'user_agent' => $logData['user_agent'],
            //     'request_data' => $logData['request_data'] ?? null,
            //     'status_code' => $logData['status_code'],
            //     'created_at' => now()
            // ]);
        } catch (\Exception $e) {
            Log::error('Failed to save activity to database: ' . $e->getMessage());
        }
    }
}
